/**
 * @NAMESPACE QNA_DRIVER_ANALYSIS.CORE.NAMESPACE
 * @COMPONENT_ID NAMESPACE_MANAGER_V1
 * @VERSION 1.0.0
 * @CREATED 2025-08-10
 * @AUTHOR jc-yap89
 * @DESCRIPTION 命名空间管理器，负责命名空间的分配、验证和规范化管理
 * 
 * @DEPENDENCIES
 * - INPUT: 模块信息、层级定义、组件类型
 * - OUTPUT: 标准化命名空间、验证结果、命名建议
 * - EXTERNAL: registry.js (中央注册系统)
 * 
 * @ARCHITECTURE_LAYER CORE
 * @RELATED_MODULES registry.js, 所有业务模块
 * 
 * @LIFECYCLE_STATUS INITIALIZED
 * @MIGRATION_STATUS NONE
 * 
 * @TAGS @MANAGER @NAMESPACE @VALIDATOR
 */

import { centralRegistry, COMPONENT_STATUS } from './registry.js';

// @SHARED_CONSTANT 根命名空间定义
export const ROOT_NAMESPACE = 'QNA_DRIVER_ANALYSIS';

// @ENUMERATION 架构层级定义
export const ARCHITECTURE_LAYERS = {
  CORE: 'CORE',           // 核心层 - 应用编排和管理
  DATA: 'DATA',           // 数据层 - 数据管理和存储
  PARSING: 'PARSING',     // 解析层 - 文本解析和AI处理
  UI: 'UI',              // 界面层 - 用户界面和交互
  REPORTS: 'REPORTS',     // 报告层 - 报告生成和导出
  QA_TOOLS: 'QA_TOOLS',  // 问答工具层 - 问答分析和优化
  UTILS: 'UTILS'         // 工具层 - 通用工具和辅助功能
};

// @SHARED_CONSTANT 层级依赖规则 (从上到下，上层可以依赖下层)
const LAYER_HIERARCHY = [
  ARCHITECTURE_LAYERS.CORE,      // 最高层
  ARCHITECTURE_LAYERS.UI,
  ARCHITECTURE_LAYERS.REPORTS,
  ARCHITECTURE_LAYERS.QA_TOOLS,
  ARCHITECTURE_LAYERS.PARSING,
  ARCHITECTURE_LAYERS.DATA,
  ARCHITECTURE_LAYERS.UTILS      // 最底层
];

// @ENUMERATION 模块类型定义
export const MODULE_TYPES = {
  // CORE层模块
  MAIN: 'MAIN',           // 主编排
  QUEUE: 'QUEUE',         // 队列管理
  REGISTRY: 'REGISTRY',   // 注册管理
  PROGRESS: 'PROGRESS',   // 进度管理
  
  // DATA层模块
  CONSTANTS: 'CONSTANTS', // 常量
  STORAGE: 'STORAGE',     // 存储
  CONFIG: 'CONFIG',       // 配置
  
  // PARSING层模块
  PARSER: 'PARSER',       // 文本解析
  LLM: 'LLM',            // AI调用
  API_CONFIG: 'API_CONFIG', // API配置
  
  // UI层模块
  CHARTS: 'CHARTS',       // 图表
  TABLES: 'TABLES',       // 表格
  PANELS: 'PANELS',       // 面板
  TABS: 'TABS',          // 标签页
  UPLOAD: 'UPLOAD',       // 上传
  
  // REPORTS层模块
  GENERATOR: 'GENERATOR', // 生成器
  EXPORTER: 'EXPORTER',   // 导出器
  HISTORY: 'HISTORY',     // 历史
  
  // QA_TOOLS层模块
  TAG_ANALYZER: 'TAG_ANALYZER', // 标签分析
  DEDUP: 'DEDUP',         // 去重
  OPTIMIZER: 'OPTIMIZER', // 优化
  
  // UTILS层模块
  LOGGER: 'LOGGER',       // 日志
  HELPERS: 'HELPERS',     // 辅助函数
  VALIDATORS: 'VALIDATORS' // 验证器
};

/**
 * @MANAGER 命名空间管理器
 * @DESCRIPTION 管理命名空间的分配、验证和规范化
 */
export class NamespaceManager {
  constructor() {
    // @INITIALIZATION 初始化层级模块映射
    this.layerModules = new Map([
      [ARCHITECTURE_LAYERS.CORE, ['MAIN', 'QUEUE', 'REGISTRY', 'PROGRESS']],
      [ARCHITECTURE_LAYERS.DATA, ['CONSTANTS', 'STORAGE', 'CONFIG']],
      [ARCHITECTURE_LAYERS.PARSING, ['PARSER', 'LLM', 'API_CONFIG']],
      [ARCHITECTURE_LAYERS.UI, ['CHARTS', 'TABLES', 'PANELS', 'TABS', 'UPLOAD']],
      [ARCHITECTURE_LAYERS.REPORTS, ['GENERATOR', 'EXPORTER', 'HISTORY']],
      [ARCHITECTURE_LAYERS.QA_TOOLS, ['TAG_ANALYZER', 'DEDUP', 'OPTIMIZER']],
      [ARCHITECTURE_LAYERS.UTILS, ['LOGGER', 'HELPERS', 'VALIDATORS']]
    ]);
    
    console.log('🏷️ 命名空间管理器已初始化'); // 中文日志
  }

  /**
   * @SERVICE 生成标准化命名空间
   * @param {string} layer - 架构层级
   * @param {string} module - 模块类型
   * @param {string} component - 组件名称
   * @param {string} version - 版本号 (默认: V1)
   * @returns {string} 标准化命名空间
   */
  generateNamespace(layer, module, component, version = 'V1') {
    // @VALIDATION 验证层级有效性
    if (!ARCHITECTURE_LAYERS[layer]) {
      throw new Error(`无效的架构层级: ${layer}`);
    }
    
    // @VALIDATION 验证模块有效性
    if (!MODULE_TYPES[module]) {
      throw new Error(`无效的模块类型: ${module}`);
    }
    
    // @VALIDATION 验证层级模块匹配
    const validModules = this.layerModules.get(layer) || [];
    if (!validModules.includes(module)) {
      throw new Error(`模块 ${module} 不属于层级 ${layer}，有效模块: ${validModules.join(', ')}`);
    }
    
    // @NORMALIZATION 规范化组件名称
    const normalizedComponent = this.normalizeComponentName(component);
    
    // @GENERATION 生成完整命名空间
    const namespace = `${ROOT_NAMESPACE}.${layer}.${module}.${normalizedComponent}_${version}`;
    
    console.log(`🏷️ 生成命名空间: ${namespace}`); // 中文日志
    return namespace;
  }

  /**
   * @SERVICE 验证命名空间格式
   * @param {string} namespace - 待验证的命名空间
   * @returns {Object} 验证结果
   */
  validateNamespace(namespace) {
    const result = {
      isValid: false,
      errors: [],
      warnings: [],
      parsed: null
    };
    
    try {
      // @VALIDATION 基本格式验证
      if (!namespace || typeof namespace !== 'string') {
        result.errors.push('命名空间不能为空且必须是字符串');
        return result;
      }
      
      // @PARSING 解析命名空间组成部分
      const parts = namespace.split('.');
      if (parts.length !== 4) {
        result.errors.push(`命名空间格式错误，应为4个部分，实际为${parts.length}个部分`);
        return result;
      }
      
      const [root, layer, module, componentWithVersion] = parts;
      
      // @VALIDATION 验证根命名空间
      if (root !== ROOT_NAMESPACE) {
        result.errors.push(`根命名空间错误，应为 ${ROOT_NAMESPACE}，实际为 ${root}`);
      }
      
      // @VALIDATION 验证架构层级
      if (!ARCHITECTURE_LAYERS[layer]) {
        result.errors.push(`无效的架构层级: ${layer}`);
      }
      
      // @VALIDATION 验证模块类型
      if (!MODULE_TYPES[module]) {
        result.errors.push(`无效的模块类型: ${module}`);
      }
      
      // @PARSING 解析组件名称和版本
      const versionMatch = componentWithVersion.match(/^(.+)_V(\d+(?:\.\d+)*)$/);
      if (!versionMatch) {
        result.errors.push('组件名称必须包含版本号，格式为 COMPONENT_NAME_V1');
      }
      
      const [, componentName, version] = versionMatch || [];
      
      // @VALIDATION 验证层级模块匹配
      if (ARCHITECTURE_LAYERS[layer] && MODULE_TYPES[module]) {
        const validModules = this.layerModules.get(layer) || [];
        if (!validModules.includes(module)) {
          result.warnings.push(`模块 ${module} 通常不属于层级 ${layer}，建议使用: ${validModules.join(', ')}`);
        }
      }
      
      // @RESULT 构建解析结果
      result.parsed = {
        root,
        layer,
        module,
        component: componentName,
        version,
        fullNamespace: namespace
      };
      
      result.isValid = result.errors.length === 0;
      
      if (result.isValid) {
        console.log(`✅ 命名空间验证通过: ${namespace}`); // 中文日志
      } else {
        console.warn(`⚠️ 命名空间验证失败: ${namespace}`, result.errors); // 中文日志
      }
      
    } catch (error) {
      result.errors.push(`验证过程中发生错误: ${error.message}`);
    }
    
    return result;
  }

  /**
   * @SERVICE 检查命名空间可用性
   * @param {string} namespace - 待检查的命名空间
   * @returns {Object} 可用性检查结果
   */
  checkAvailability(namespace) {
    const result = {
      isAvailable: false,
      conflictType: null,
      conflictDetails: null,
      suggestions: []
    };
    
    // @VALIDATION 首先验证命名空间格式
    const validation = this.validateNamespace(namespace);
    if (!validation.isValid) {
      result.conflictType = 'INVALID_FORMAT';
      result.conflictDetails = validation.errors;
      return result;
    }
    
    // @CONFLICT_CHECK 检查是否已被注册
    const existingNamespace = centralRegistry.getNamespace(namespace);
    if (existingNamespace) {
      result.conflictType = 'ALREADY_REGISTERED';
      result.conflictDetails = `命名空间已被组件 ${existingNamespace.componentId} 使用`;
      
      // @SUGGESTION 生成替代建议
      const parsed = validation.parsed;
      result.suggestions = this.generateAlternativeNamespaces(parsed);
      
      return result;
    }
    
    result.isAvailable = true;
    console.log(`✅ 命名空间可用: ${namespace}`); // 中文日志
    return result;
  }

  /**
   * @SERVICE 为组件分配命名空间
   * @param {Object} componentInfo - 组件信息
   * @param {string} componentInfo.filePath - 文件路径
   * @param {string} componentInfo.componentName - 组件名称
   * @param {string} componentInfo.type - 组件类型标签
   * @returns {string} 分配的命名空间
   */
  allocateNamespace(componentInfo) {
    const { filePath, componentName, type } = componentInfo;
    
    // @INFERENCE 根据文件路径推断层级和模块
    const { layer, module } = this.inferLayerAndModule(filePath, type);
    
    // @GENERATION 生成命名空间
    let namespace = this.generateNamespace(layer, module, componentName);
    
    // @AVAILABILITY_CHECK 检查可用性
    let availability = this.checkAvailability(namespace);
    
    // @CONFLICT_RESOLUTION 如果冲突，尝试替代方案
    if (!availability.isAvailable && availability.suggestions.length > 0) {
      for (const suggestion of availability.suggestions) {
        const suggestionAvailability = this.checkAvailability(suggestion);
        if (suggestionAvailability.isAvailable) {
          namespace = suggestion;
          availability = suggestionAvailability;
          break;
        }
      }
    }
    
    if (!availability.isAvailable) {
      throw new Error(`无法为组件 ${componentName} 分配命名空间: ${availability.conflictDetails}`);
    }
    
    console.log(`🎯 为组件 ${componentName} 分配命名空间: ${namespace}`); // 中文日志
    return namespace;
  }

  /**
   * @UTIL 规范化组件名称
   * @param {string} componentName - 原始组件名称
   * @returns {string} 规范化后的组件名称
   */
  normalizeComponentName(componentName) {
    return componentName
      .replace(/[^a-zA-Z0-9_]/g, '_')  // 替换非法字符为下划线
      .replace(/_+/g, '_')            // 合并多个下划线
      .replace(/^_|_$/g, '')          // 移除首尾下划线
      .toUpperCase();                 // 转换为大写
  }

  /**
   * @UTIL 根据文件路径和类型推断层级和模块
   * @param {string} filePath - 文件路径
   * @param {string} type - 组件类型标签
   * @returns {Object} 推断结果
   */
  inferLayerAndModule(filePath, type) {
    // @INFERENCE 基于文件名推断
    const fileName = filePath.split('/').pop().replace('.js', '');
    
    // @MAPPING 文件名到层级模块的映射
    const fileMapping = {
      'constants': { layer: ARCHITECTURE_LAYERS.DATA, module: MODULE_TYPES.CONSTANTS },
      'storage': { layer: ARCHITECTURE_LAYERS.DATA, module: MODULE_TYPES.STORAGE },
      'parser': { layer: ARCHITECTURE_LAYERS.PARSING, module: MODULE_TYPES.PARSER },
      'llm': { layer: ARCHITECTURE_LAYERS.PARSING, module: MODULE_TYPES.LLM },
      'charts': { layer: ARCHITECTURE_LAYERS.UI, module: MODULE_TYPES.CHARTS },
      'drag-upload': { layer: ARCHITECTURE_LAYERS.UI, module: MODULE_TYPES.UPLOAD },
      'registry': { layer: ARCHITECTURE_LAYERS.CORE, module: MODULE_TYPES.REGISTRY },
      'namespace-manager': { layer: ARCHITECTURE_LAYERS.CORE, module: MODULE_TYPES.REGISTRY }
    };
    
    // @FALLBACK 基于类型标签推断
    const typeMapping = {
      '@MANAGER': { layer: ARCHITECTURE_LAYERS.CORE, module: MODULE_TYPES.REGISTRY },
      '@SERVICE': { layer: ARCHITECTURE_LAYERS.PARSING, module: MODULE_TYPES.PARSER },
      '@UTIL': { layer: ARCHITECTURE_LAYERS.UTILS, module: MODULE_TYPES.HELPERS },
      '@COMPONENT': { layer: ARCHITECTURE_LAYERS.UI, module: MODULE_TYPES.PANELS }
    };
    
    return fileMapping[fileName] || typeMapping[type] || { 
      layer: ARCHITECTURE_LAYERS.UTILS, 
      module: MODULE_TYPES.HELPERS 
    };
  }

  /**
   * @UTIL 生成替代命名空间建议
   * @param {Object} parsed - 解析后的命名空间信息
   * @returns {Array} 替代建议列表
   */
  generateAlternativeNamespaces(parsed) {
    const suggestions = [];
    const { root, layer, module, component } = parsed;
    
    // @SUGGESTION 版本递增建议
    for (let v = 2; v <= 5; v++) {
      suggestions.push(`${root}.${layer}.${module}.${component}_V${v}`);
    }
    
    // @SUGGESTION 组件名称变体建议
    const variants = [
      `${component}_ALT`,
      `${component}_NEW`,
      `${component}_IMPL`,
      `${component}_EXT`
    ];
    
    variants.forEach(variant => {
      suggestions.push(`${root}.${layer}.${module}.${variant}_V1`);
    });
    
    return suggestions;
  }

  /**
   * @SERVICE 获取层级依赖规则
   * @returns {Array} 层级依赖顺序
   */
  getLayerHierarchy() {
    return [...LAYER_HIERARCHY];
  }

  /**
   * @SERVICE 验证层级依赖关系
   * @param {string} sourceLayer - 源层级
   * @param {string} targetLayer - 目标层级
   * @returns {boolean} 依赖关系是否合法
   */
  validateLayerDependency(sourceLayer, targetLayer) {
    const sourceIndex = LAYER_HIERARCHY.indexOf(sourceLayer);
    const targetIndex = LAYER_HIERARCHY.indexOf(targetLayer);
    
    // 上层可以依赖下层，同层可以相互依赖
    return sourceIndex <= targetIndex;
  }
}

// @SINGLETON 创建全局实例
export const namespaceManager = new NamespaceManager();
