# 冲突检测规则

**规则版本**: 1.0.0  
**最后更新**: 2025-08-10  
**维护者**: jc-yap89  
**相关性评分**: [RS:4]

## 冲突检测概述 [RS:5]

冲突检测系统负责识别和预防项目中可能出现的各种冲突，确保架构的一致性和代码的质量。系统采用多层次的检测机制，从命名空间冲突到功能重复，全面保护项目的健康发展。

## 冲突类型定义 [RS:5]

### 1. 命名空间冲突 (NAMESPACE_COLLISION)
**定义**: 相同的命名空间被多个不同的组件使用

**检测规则**:
- 扫描所有文件的@NAMESPACE声明
- 检查中央注册表中的命名空间唯一性
- 验证命名空间格式的正确性

**触发条件**:
```javascript
// 冲突示例
// 文件A: constants.js
@NAMESPACE QNA_DRIVER_ANALYSIS.DATA.CONSTANTS.MAX_CONCURRENCY_V1

// 文件B: config.js  
@NAMESPACE QNA_DRIVER_ANALYSIS.DATA.CONSTANTS.MAX_CONCURRENCY_V1 // ❌ 冲突
```

**解决策略**:
1. 重新分配命名空间
2. 合并重复功能
3. 版本号递增
4. 添加区分性后缀

### 2. 功能重复冲突 (FUNCTION_DUPLICATION)
**定义**: 不同组件实现了相同或高度相似的功能

**检测规则**:
- 分析函数名称相似度
- 检查函数参数和返回值类型
- 比较函数功能描述的语义相似性
- 识别代码逻辑的重复模式

**触发条件**:
```javascript
// 冲突示例
// 文件A: parser.js
export const parseTimestamp = (dateStr, timeStr) => { /* 实现A */ }

// 文件B: utils.js
export const parseDateTime = (date, time) => { /* 相似实现 */ } // ⚠️ 可能重复
```

**解决策略**:
1. 合并重复功能到公共模块
2. 建立功能继承关系
3. 明确功能边界和差异
4. 重构为可复用组件

### 3. 循环依赖冲突 (CIRCULAR_DEPENDENCY)
**定义**: 组件间形成循环依赖关系

**检测规则**:
- 构建依赖关系图
- 使用深度优先搜索检测环路
- 分析import/export语句
- 检查运行时依赖调用

**触发条件**:
```javascript
// 冲突示例
// 文件A: parser.js
import { validateData } from './validator.js';

// 文件B: validator.js  
import { parseContent } from './parser.js'; // ❌ 循环依赖
```

**解决策略**:
1. 重构依赖关系
2. 提取公共依赖到独立模块
3. 使用依赖注入模式
4. 重新设计模块边界

### 4. 层级违规冲突 (LAYER_VIOLATION)
**定义**: 违反架构分层原则的跨层调用

**检测规则**:
- 验证依赖方向符合层级规则
- 检查import语句的层级合规性
- 分析函数调用的层级关系
- 监控运行时的跨层访问

**触发条件**:
```javascript
// 冲突示例
// DATA层组件调用UI层组件
// 文件: storage.js (DATA层)
import { updateChart } from './charts.js'; // ❌ 下层调用上层
```

**解决策略**:
1. 重新设计调用关系
2. 使用事件机制解耦
3. 引入中介者模式
4. 重构模块职责

### 5. 版本冲突 (VERSION_CONFLICT)
**定义**: 同一组件的不同版本之间存在不兼容问题

**检测规则**:
- 检查版本号的语义化规范
- 验证向后兼容性
- 分析接口变更影响
- 监控版本依赖关系

**触发条件**:
```javascript
// 冲突示例
// 组件A依赖PARSER_V1，组件B依赖PARSER_V2
// 且V1和V2接口不兼容
```

**解决策略**:
1. 维护版本兼容性
2. 提供迁移路径
3. 并行维护多版本
4. 统一版本升级

## 检测机制实现 [RS:4]

### 自动检测流程
```javascript
// 检测流程伪代码
const conflictDetection = {
  // 1. 扫描阶段
  scanPhase: () => {
    scanAllFiles();           // 扫描所有源文件
    parseNamespaces();        // 解析命名空间声明
    buildDependencyGraph();   // 构建依赖关系图
    extractFunctionSignatures(); // 提取函数签名
  },
  
  // 2. 分析阶段
  analysisPhase: () => {
    detectNamespaceCollisions();  // 检测命名空间冲突
    findFunctionDuplication();    // 查找功能重复
    checkCircularDependencies();  // 检查循环依赖
    validateLayerCompliance();    // 验证层级合规性
    analyzeVersionConflicts();    // 分析版本冲突
  },
  
  // 3. 报告阶段
  reportPhase: () => {
    generateConflictReport();     // 生成冲突报告
    suggestResolutions();         // 提供解决建议
    updateConflictRegistry();     // 更新冲突注册表
  }
};
```

### 检测触发时机
1. **组件注册时**: 新组件注册到中央注册表时
2. **文件保存时**: 开发过程中文件保存时
3. **构建时**: 项目构建或部署前
4. **定期检查**: 每日/每周的定期检查
5. **手动触发**: 开发者手动执行检查

### 检测精度控制
```javascript
// 检测精度配置
const detectionConfig = {
  namespaceCollision: {
    enabled: true,
    strictMode: true        // 严格模式：完全匹配
  },
  functionDuplication: {
    enabled: true,
    similarityThreshold: 0.8, // 相似度阈值
    ignorePrivateFunctions: true
  },
  circularDependency: {
    enabled: true,
    maxDepth: 10           // 最大检测深度
  },
  layerViolation: {
    enabled: true,
    allowSameLayer: true   // 允许同层调用
  },
  versionConflict: {
    enabled: true,
    checkCompatibility: true
  }
};
```

## 冲突解决策略 [RS:3]

### 自动解决策略
1. **命名空间自动重命名**: 为冲突的命名空间自动生成新名称
2. **依赖关系重构**: 自动调整简单的依赖关系问题
3. **版本号自动递增**: 为新版本自动分配版本号
4. **导入语句更新**: 自动更新受影响的导入语句

### 半自动解决策略
1. **冲突提示和建议**: 提供详细的冲突信息和解决建议
2. **交互式解决向导**: 引导开发者逐步解决冲突
3. **批量操作支持**: 支持批量处理相似的冲突
4. **预览和确认**: 在应用解决方案前提供预览

### 手动解决策略
1. **详细冲突报告**: 提供完整的冲突分析报告
2. **解决方案文档**: 提供标准的解决方案文档
3. **最佳实践指导**: 提供避免冲突的最佳实践
4. **专家咨询支持**: 复杂冲突的专家支持

## 预防机制 [RS:3]

### 开发时预防
1. **实时检测**: 在代码编写过程中实时检测潜在冲突
2. **智能提示**: 提供命名空间和函数名的智能建议
3. **模板规范**: 提供标准化的代码模板
4. **规范检查**: 自动检查代码规范符合性

### 设计时预防
1. **架构审查**: 在设计阶段进行架构审查
2. **依赖规划**: 提前规划组件依赖关系
3. **命名空间规划**: 统一规划命名空间分配
4. **接口设计**: 标准化接口设计规范

### 流程预防
1. **代码审查**: 强制性的代码审查流程
2. **自动化测试**: 包含冲突检测的自动化测试
3. **持续集成**: CI/CD流程中的冲突检测
4. **定期审计**: 定期的架构健康度审计

## 监控和报告 [RS:3]

### 冲突监控指标
- 冲突总数和类型分布
- 冲突解决时间和效率
- 冲突复发率
- 架构健康度评分

### 报告格式
```markdown
# 冲突检测报告

## 概要
- 检测时间: 2025-08-10 14:30:00
- 扫描文件数: 15
- 发现冲突数: 3
- 严重程度: 中等

## 冲突详情
### 1. 命名空间冲突
- 类型: NAMESPACE_COLLISION
- 位置: constants.js:1, config.js:1
- 描述: 命名空间重复使用
- 建议: 重新分配命名空间

### 2. 功能重复
- 类型: FUNCTION_DUPLICATION
- 位置: parser.js:28, utils.js:45
- 相似度: 85%
- 建议: 合并到公共模块

## 解决建议
1. 立即处理严重冲突
2. 计划解决中等冲突
3. 监控轻微冲突
```

### 集成和通知
- 集成到开发环境
- 邮件/消息通知
- 仪表板展示
- API接口提供
