/**
 * storage.js - 本地存储管理服务
 * @SERVICE 本地存储管理服务
 * 创建日期: 2024-06-13
 */

// @CONSTANT 存储键名
export const STORAGE_KEYS = {
  DRIVERS: 'qna_drivers',
  CUSTOMER_SERVICE: 'qna_customer_service',
  SUPPORT_AGENTS: 'qna_support_agents', // 新增：客服统计数据
  KNOWLEDGE: 'qna_knowledge',
  METRICS: 'qna_metrics',
  QUESTION_CATEGORIES: 'qna_question_categories',
  QUESTION_TAGS: 'qna_question_tags', // 新增：问题标签统计
  DETAILED_QUESTIONS: 'qna_detailed_questions', // 新增：详细问题记录
  QA_DATASET: 'qna_qa_dataset', // 新增：问答题集数据
  SETTINGS: 'qna_settings',
  // 新增：数据持久化相关
  PROCESSING_STATE: 'qna_processing_state', // 处理状态
  FILE_REGISTRY: 'qna_file_registry', // 文件注册表
  ANALYSIS_PROGRESS: 'qna_analysis_progress', // 分析进度
  API_CONFIG: 'qna_api_config', // API配置
  // 新增：报告相关
  REPORT_HISTORY: 'qna_report_history', // 报告历史记录
  REPORT_TEMPLATES: 'qna_report_templates', // 报告模板
  REPORT_CONFIG: 'qna_report_config' // 报告配置
};

// @CONFIG_FILE 旧版本地存储键名（增强页） — 用于迁移到标准前缀qna_*
export const LEGACY_ENHANCED_KEYS = { // 旧键映射，来源于 index-enhanced.html 中内联实现
  DRIVERS: 'enhanced_qna_drivers', // 旧：司机数据
  KNOWLEDGE: 'enhanced_qna_knowledge', // 旧：知识库
  METRICS: 'enhanced_qna_metrics', // 旧：指标
  QUESTION_CATEGORIES: 'enhanced_qna_categories', // 旧：问题类别
  SETTINGS: 'enhanced_qna_settings' // 旧：设置（含API Key等）
  // 提示：如存在 enhanced_qna_session 等临时键，这里不迁移（非持久业务数据）
};

// @SERVICE 本地存储管理器
export class StorageManager {
  constructor() {
    this.isAvailable = this.checkStorageAvailability(); // 初始化可用性检查
    this.tryMigrateLegacyKeys(); // @MIGRATION 尝试从增强版键迁移到标准键
  }

  // @UTIL 检查localStorage可用性
  checkStorageAvailability() {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch (e) {
      console.warn('localStorage不可用:', e);
      return false;
    }
  }

  // @MIGRATION 从增强版 localStorage 键名迁移到标准 qna_* 键名（一次性）
  tryMigrateLegacyKeys() {
    if (!this.isAvailable) return; // localStorage 不可用则跳过

    const MIGRATION_FLAG = 'qna_migrated_v2'; // 迁移幂等标记 - 更新版本号
    try {
      if (localStorage.getItem(MIGRATION_FLAG)) {
        return; // 已迁移过，避免重复
      }

      // 迁移规则：若新键为空或不存在，且旧键存在，则直接迁移；若两者同时存在，保留新键并删除旧键
      const migrateIfNeeded = (oldKey, newKey) => {
        const oldVal = localStorage.getItem(oldKey); // 读取旧值
        const newVal = localStorage.getItem(newKey); // 读取新值

        if (oldVal && !newVal) {
          // 仅当新键不存在时迁移，确保不覆盖已有数据
          localStorage.setItem(newKey, oldVal); // 迁移数据
        }
        if (oldVal) {
          localStorage.removeItem(oldKey); // 移除旧键，避免混用
        }
      };

      migrateIfNeeded(LEGACY_ENHANCED_KEYS.DRIVERS, STORAGE_KEYS.DRIVERS);
      migrateIfNeeded(LEGACY_ENHANCED_KEYS.KNOWLEDGE, STORAGE_KEYS.KNOWLEDGE);
      migrateIfNeeded(LEGACY_ENHANCED_KEYS.METRICS, STORAGE_KEYS.METRICS);
      migrateIfNeeded(LEGACY_ENHANCED_KEYS.QUESTION_CATEGORIES, STORAGE_KEYS.QUESTION_CATEGORIES);
      migrateIfNeeded(LEGACY_ENHANCED_KEYS.SETTINGS, STORAGE_KEYS.SETTINGS);

      // 清理旧版本迁移标记
      localStorage.removeItem('qna_migrated_v1');
      localStorage.setItem(MIGRATION_FLAG, 'true'); // 打标成功
    } catch (e) {
      console.warn('迁移增强版存储键失败（已忽略）：', e); // 记录但不中断流程
    }
  }

  // @SERVICE 保存数据到localStorage
  save(key, data) {
    if (!this.isAvailable) {
      console.warn('存储不可用，数据未保存');
      return false;
    }

    try {
      const jsonData = JSON.stringify(data);
      localStorage.setItem(key, jsonData);
      return true;
    } catch (e) {
      console.error('保存数据失败:', e);
      return false;
    }
  }

  // @SERVICE 从localStorage加载数据
  load(key, defaultValue = null) {
    if (!this.isAvailable) {
      return defaultValue;
    }

    try {
      const jsonData = localStorage.getItem(key);
      return jsonData ? JSON.parse(jsonData) : defaultValue;
    } catch (e) {
      console.error('加载数据失败:', e);
      return defaultValue;
    }
  }

  // @SERVICE 删除指定键的数据
  remove(key) {
    if (!this.isAvailable) {
      return false;
    }

    try {
      localStorage.removeItem(key);
      return true;
    } catch (e) {
      console.error('删除数据失败:', e);
      return false;
    }
  }

  // @SERVICE 清空所有应用数据
  clearAll() {
    if (!this.isAvailable) {
      return false;
    }

    try {
      // 清理标准键
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
      // 额外清理可能残留的增强版旧键，确保环境整洁
      Object.values(LEGACY_ENHANCED_KEYS).forEach(oldKey => {
        localStorage.removeItem(oldKey);
      });
      return true;
    } catch (e) {
      console.error('清空数据失败:', e);
      return false;
    }
  }

  // @SERVICE 获取存储使用情况
  getStorageInfo() {
    if (!this.isAvailable) {
      return { available: false };
    }

    let totalSize = 0;
    const itemSizes = {};

    Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
      const data = localStorage.getItem(key);
      const size = data ? new Blob([data]).size : 0;
      itemSizes[name] = size;
      totalSize += size;
    });

    return {
      available: true,
      totalSize,
      itemSizes,
      totalSizeFormatted: this.formatBytes(totalSize)
    };
  }

  // @UTIL 格式化字节大小
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
