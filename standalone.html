		// LLM 对话分析函数已迁移到 llm.js
		.header-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: var(--spacing-lg) 0;
			flex-wrap: wrap;
			gap: var(--spacing-md);
		}

		.app-title {
			font-size: 1.5rem;
			font-weight: 700;
			color: var(--primary);
			display: flex;
			align-items: center;
			gap: var(--spacing-sm);
		}

		/* 主网格布局 */
		.main-grid {
			display: grid;
			grid-template-columns: 1fr;
			gap: var(--spacing-xl);
			margin-bottom: var(--spacing-2xl);
		}

		/* 卡片组件 */
		.card {
			background: var(--white);
			border-radius: var(--radius-lg);
			box-shadow: var(--shadow-sm);
			border: 1px solid var(--gray-200);
			overflow: hidden;
			margin-bottom: var(--spacing-lg);
		}

		.card-header {
			padding: var(--spacing-lg);
			border-bottom: 1px solid var(--gray-200);
			background: var(--gray-50);
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		.card-header h2 {
			font-size: 1.125rem;
			font-weight: 600;
			color: var(--gray-800);
			display: flex;
			align-items: center;
			gap: var(--spacing-sm);
		}

		.card-header h2 i {
			color: var(--primary);
		}

		/* 上传区域 */
		.upload-area {
			padding: var(--spacing-lg);
		}

		.file-input-label {
			display: inline-flex;
			align-items: center;
			gap: var(--spacing-sm);
			padding: var(--spacing-md) var(--spacing-lg);
			background: var(--primary);
			color: var(--white);
			border-radius: var(--radius-md);
			cursor: pointer;
			font-weight: 500;
			transition: all 0.2s ease;
			border: none;
			font-size: 0.875rem;
			margin-bottom: var(--spacing-md);
		}

		.file-input-label:hover {
			background: var(--primary-dark);
		}

		#file-input {
			display: none;
		}

		/* 拖拽上传区域 */
		.drag-drop-area {
			border: 2px dashed var(--gray-300);
			border-radius: var(--radius-lg);
			padding: var(--spacing-2xl);
			text-align: center;
			cursor: pointer;
			transition: all 0.3s ease;
			background: var(--gray-50);
			margin-bottom: var(--spacing-lg);
		}

		.drag-drop-area:hover {
			border-color: var(--primary);
			background: rgba(59, 130, 246, 0.05);
		}

		.drag-drop-area.drag-over {
			border-color: var(--primary);
			background: rgba(59, 130, 246, 0.1);
			transform: scale(1.02);
		}

		.drag-icon {
			font-size: 3rem;
			color: var(--gray-400);
			margin-bottom: var(--spacing-md);
		}

		.drag-title {
			font-size: 1.125rem;
			font-weight: 600;
			color: var(--gray-700);
			margin-bottom: var(--spacing-sm);
		}

		/* 文件列表样式 */
		.file-list {
			max-height: 200px;
			overflow-y: auto;
			border: 1px solid var(--gray-200);
			border-radius: var(--radius-md);
			padding: var(--spacing-md);
			background: var(--white);
		}

		.file-item {
			padding: var(--spacing-sm);
			border: 1px solid var(--gray-200);
			border-radius: var(--radius-sm);
			margin-bottom: var(--spacing-sm);
			background: var(--gray-50);
		}

		.file-item:last-child {
			margin-bottom: 0;
		}

		/* 工具类样式 */
		.text-muted {
			color: var(--gray-500) !important;
		}

		.d-flex {
			display: flex !important;
		}

		.justify-content-between {
			justify-content: space-between !important;
		}

		.align-items-center {
			align-items: center !important;
		}

		.mb-2 {
			margin-bottom: 0.5rem !important;
		}

		.mt-3 {
			margin-top: 1rem !important;
		}

		.p-2 {
			padding: 0.5rem !important;
		}

		.border {
			border: 1px solid var(--gray-200) !important;
		}

		.rounded {
			border-radius: var(--radius-sm) !important;
		}

		.d-block {
			display: block !important;
		}

		.btn-sm {
			padding: 0.25rem 0.5rem;
			font-size: 0.875rem;
		}

		.btn-outline-danger {
			color: var(--danger);
			border: 1px solid var(--danger);
			background: transparent;
		}

		.btn-outline-danger:hover {
			color: var(--white);
			background: var(--danger);
		}

		/* 按钮组件 */
		.btn-primary {
			display: flex;
			align-items: center;
			gap: var(--spacing-sm);
			padding: var(--spacing-md) var(--spacing-lg);
			background: var(--primary);
			color: var(--white);
			border-radius: var(--radius-md);
			font-weight: 500;
			font-size: 0.875rem;
			cursor: pointer;
			transition: all 0.2s ease;
			border: none;
		}

		.btn-primary:hover:not(:disabled) {
			background: var(--primary-dark);
		}

		.btn-primary:disabled {
			background: var(--gray-400);
			cursor: not-allowed;
		}

		/* 日志显示 */
		.log-card {
			margin-top: var(--spacing-lg);
		}

		.log-box {
			height: 300px;
			overflow-y: auto;
			background: var(--gray-900);
			color: #10b981;
			padding: var(--spacing-md);
			font-family: 'Courier New', monospace;
			font-size: 0.75rem;
			line-height: 1.4;
		}

		.log-box p {
			margin: 0 0 var(--spacing-xs);
			word-wrap: break-word;
		}

		/* 进度显示 */
		.progress-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: var(--spacing-md);
			padding: var(--spacing-lg);
		}

		.progress-track {
			height: 8px;
			background: var(--gray-200);
			border-radius: var(--radius-md);
			overflow: hidden;
			margin: 0 var(--spacing-lg) var(--spacing-lg);
		}

		.progress-fill {
			height: 100%;
			background: linear-gradient(90deg, var(--primary) 0%, var(--primary-dark) 100%);
			border-radius: var(--radius-md);
			transition: width 0.5s ease;
			width: 0%;
		}

		/* 仪表盘样式 */
		.dashboard-grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: var(--spacing-md);
			padding: var(--spacing-lg);
		}

		.metric-item {
			display: flex;
			align-items: center;
			gap: var(--spacing-md);
			padding: var(--spacing-md);
			background: var(--gray-50);
			border-radius: var(--radius-md);
			border: 1px solid var(--gray-200);
		}

		.metric-icon {
			width: 48px;
			height: 48px;
			border-radius: 50%;
			background: var(--primary);
			color: var(--white);
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 1.25rem;
			flex-shrink: 0;
		}

		.metric-content {
			flex: 1;
		}

		.metric-value {
			font-size: 1.5rem;
			font-weight: 700;
			color: var(--gray-900);
			line-height: 1;
		}

		.metric-label {
			font-size: 0.75rem;
			color: var(--gray-600);
			margin-top: var(--spacing-xs);
		}

		/* 进度控制按钮 */
		.progress-controls {
			display: flex;
			gap: var(--spacing-sm);
		}

		.btn-icon {
			background: transparent;
			border: 1px solid var(--gray-300);
			color: var(--gray-600);
			cursor: pointer;
			padding: var(--spacing-sm);
			border-radius: var(--radius-sm);
			transition: all 0.2s ease;
		}

		.btn-icon:hover {
			background: var(--gray-100);
			color: var(--gray-800);
		}

		/* 进度详情 */
		.progress-details {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			gap: var(--spacing-sm);
			padding: 0 var(--spacing-lg) var(--spacing-lg);
		}

		.detail-item {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-xs);
			text-align: center;
			padding: var(--spacing-sm);
			background: var(--gray-50);
			border-radius: var(--radius-sm);
			font-size: 0.75rem;
		}

		.detail-item span:first-child {
			color: var(--gray-500);
			font-weight: 500;
		}

		.detail-item span:last-child {
			color: var(--gray-800);
			font-weight: 600;
			font-size: 0.875rem;
		}

		/* 图表样式 */
		.charts-section {
			margin-top: var(--spacing-xl);
		}

		.charts {
			display: grid;
			grid-template-columns: repeat(2, minmax(280px, 1fr));
			gap: var(--spacing-lg);
			margin-top: var(--spacing-lg);
		}

		.chart-container {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-md);
		}

		.chart {
			height: 320px;
			border: 1px solid var(--gray-200);
			border-radius: var(--radius-lg);
			background: var(--white);
		}

		.chart-preview {
			background: var(--white);
			border: 1px solid var(--gray-200);
			border-radius: var(--radius-md);
			padding: var(--spacing-md);
		}

		.chart-preview h4 {
			margin: 0 0 var(--spacing-sm) 0;
			font-size: 0.875rem;
			font-weight: 600;
			color: var(--gray-700);
		}

		.chart-table-container {
			max-height: 200px;
			overflow-y: auto;
		}

		.chart-preview-table {
			width: 100%;
			border-collapse: collapse;
			font-size: 0.75rem;
		}

		.chart-preview-table th {
			background: var(--gray-50);
			padding: var(--spacing-xs);
			text-align: left;
			font-weight: 600;
			color: var(--gray-600);
			border-bottom: 1px solid var(--gray-200);
			position: sticky;
			top: 0;
		}

		.chart-preview-table td {
			padding: var(--spacing-xs);
			border-bottom: 1px solid var(--gray-100);
			vertical-align: top;
		}

		.chart-preview-table tr:hover {
			background: var(--gray-50);
		}

		.no-data {
			text-align: center;
			color: var(--gray-400);
			font-style: italic;
		}

		/* 实时结果表格样式 */
		.results-table-container {
			max-height: 400px;
			overflow-y: auto;
			border: 1px solid var(--gray-200);
			border-radius: var(--radius-md);
		}

		.results-table {
			width: 100%;
			border-collapse: collapse;
			font-size: 0.875rem;
		}

		.results-table th {
			background: var(--gray-50);
			padding: var(--spacing-sm) var(--spacing-xs);
			text-align: left;
			font-weight: 600;
			color: var(--gray-700);
			border-bottom: 1px solid var(--gray-200);
			position: sticky;
			top: 0;
			z-index: 1;
		}

		.results-table td {
			padding: var(--spacing-sm) var(--spacing-xs);
			border-bottom: 1px solid var(--gray-100);
			vertical-align: top;
		}

		.results-table tr:hover {
			background: var(--gray-50);
		}

		.status-badge {
			display: inline-block;
			padding: 2px 6px;
			border-radius: 4px;
			font-size: 0.75rem;
			font-weight: 500;
		}

		.status-processing {
			background: var(--warning);
			color: var(--white);
		}

		.status-completed {
			background: var(--success);
			color: var(--white);
		}

		.status-error {
			background: var(--danger);
			color: var(--white);
		}

		.question-cell {
			max-width: 200px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.score-cell {
			text-align: center;
			font-weight: 600;
		}

		.score-high { color: var(--success); }
		.score-medium { color: var(--warning); }
		.score-low { color: var(--danger); }

		/* 响应式设计 */
		@media (min-width: 768px) {
			.main-grid {
				grid-template-columns: 1fr 1fr;
			}
			
			.dashboard-grid {
				grid-template-columns: repeat(2, 1fr);
			}
		}

		@media (min-width: 1024px) {
			.dashboard-grid {
				grid-template-columns: repeat(4, 1fr);
			}
		}

		/* 标签页样式 */
		.tabs-container {
			margin-bottom: var(--spacing-xl);
		}

		.tabs-nav {
			display: flex;
			background: var(--white);
			border-radius: var(--radius-lg);
			box-shadow: var(--shadow-sm);
			border: 1px solid var(--gray-200);
			overflow: hidden;
		}

		.tab-btn {
			flex: 1;
			padding: var(--spacing-md) var(--spacing-lg);
			background: var(--gray-50);
			border: none;
			border-right: 1px solid var(--gray-200);
			color: var(--gray-600);
			font-weight: 500;
			cursor: pointer;
			transition: all 0.2s ease;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: var(--spacing-sm);
		}

		.tab-btn:last-child {
			border-right: none;
		}

		.tab-btn:hover {
			background: var(--gray-100);
			color: var(--gray-800);
		}

		.tab-btn.active {
			background: var(--primary);
			color: var(--white);
		}

		.tab-content {
			display: none;
		}

		.tab-content.active {
			display: block;
		}

		/* 问答题集样式 */
		.qa-dataset-container {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-lg);
		}

		/* 详细报告样式 */
		.report-section {
			margin-bottom: var(--spacing-lg);
			padding: var(--spacing-md);
			border: 1px solid var(--border-color);
			border-radius: var(--border-radius);
			background: var(--bg-secondary);
		}

		.report-section h3 {
			margin: 0 0 var(--spacing-md) 0;
			color: var(--text-primary);
			font-size: 1.1rem;
			display: flex;
			align-items: center;
			gap: var(--spacing-sm);
		}

		.config-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
			gap: var(--spacing-md);
		}

		.config-item {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-sm);
		}

		.config-item label {
			font-weight: 600;
			color: var(--text-primary);
		}

		.file-selection {
			border: 2px dashed var(--border-color);
			border-radius: var(--border-radius);
			padding: var(--spacing-md);
			text-align: center;
			background: var(--bg-primary);
		}

		.selection-summary {
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: var(--spacing-sm);
		}

		.date-range {
			display: flex;
			align-items: center;
			gap: var(--spacing-sm);
		}

		.date-range span {
			color: var(--text-secondary);
			font-size: 0.9rem;
		}

		.format-checkboxes {
			display: flex;
			gap: var(--spacing-md);
		}

		.format-checkboxes label {
			display: flex;
			align-items: center;
			gap: var(--spacing-xs);
			font-weight: normal;
			cursor: pointer;
		}

		.progress-container {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-md);
		}

		.progress-bar {
			width: 100%;
			height: 8px;
			background: var(--gray-200);
			border-radius: 4px;
			overflow: hidden;
		}

		.progress-fill {
			height: 100%;
			background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
			border-radius: 4px;
			transition: width 0.3s ease;
			width: 0%;
		}

		.progress-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.progress-details {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-xs);
			font-size: 0.9rem;
			color: var(--text-secondary);
		}

		.report-actions {
			display: flex;
			gap: var(--spacing-sm);
			margin-bottom: var(--spacing-md);
		}

		.report-preview {
			border: 1px solid var(--border-color);
			border-radius: var(--border-radius);
			padding: var(--spacing-lg);
			background: white;
			max-height: 600px;
			overflow-y: auto;
		}

		.history-list {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-sm);
		}

		.history-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: var(--spacing-md);
			border: 1px solid var(--border-color);
			border-radius: var(--border-radius);
			background: var(--bg-primary);
		}

		.history-item-info {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-xs);
		}

		.history-item-title {
			font-weight: 600;
			color: var(--text-primary);
		}

		.history-item-meta {
			font-size: 0.85rem;
			color: var(--text-secondary);
		}

		.history-item-actions {
			display: flex;
			gap: var(--spacing-xs);
		}

		.empty-state {
			text-align: center;
			padding: var(--spacing-xl);
			color: var(--text-secondary);
		}

		.empty-state i {
			font-size: 3rem;
			margin-bottom: var(--spacing-md);
			opacity: 0.5;
		}

		.hidden {
			display: none !important;
		}

		.qa-toolbar {
			display: flex;
			gap: var(--spacing-md);
			align-items: center;
			flex-wrap: wrap;
		}

		.search-box {
			position: relative;
			flex: 1;
			min-width: 250px;
		}

		.search-box input {
			padding-right: 40px;
		}

		.search-box i {
			position: absolute;
			right: 12px;
			top: 50%;
			transform: translateY(-50%);
			color: var(--gray-400);
		}

		.filter-controls {
			display: flex;
			gap: var(--spacing-sm);
			align-items: center;
		}

		.qa-stats-grid, .main-stats-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
			gap: var(--spacing-md);
			padding: var(--spacing-lg);
		}

		.stat-item {
			display: flex;
			align-items: center;
			gap: var(--spacing-md);
			padding: var(--spacing-md);
			background: var(--gray-50);
			border-radius: var(--radius-md);
		}

		.stat-icon {
			width: 48px;
			height: 48px;
			background: var(--primary);
			color: var(--white);
			border-radius: var(--radius-md);
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 1.25rem;
		}

		.stat-content {
			flex: 1;
		}

		.stat-value {
			font-size: 1.5rem;
			font-weight: 700;
			color: var(--gray-800);
		}

		.stat-label {
			font-size: 0.875rem;
			color: var(--gray-600);
		}

		.qa-list-container {
			max-height: 600px;
			overflow-y: auto;
			padding: var(--spacing-lg);
		}

		.qa-empty-state {
			text-align: center;
			padding: var(--spacing-2xl);
			color: var(--gray-500);
		}

		.qa-empty-state i {
			font-size: 3rem;
			margin-bottom: var(--spacing-md);
			color: var(--gray-300);
		}

		.qa-item {
			border: 1px solid var(--gray-200);
			border-radius: var(--radius-md);
			margin-bottom: var(--spacing-md);
			background: var(--white);
			transition: all 0.2s ease;
		}

		.qa-item:hover {
			box-shadow: var(--shadow-md);
			border-color: var(--primary);
		}

		.qa-item-header {
			padding: var(--spacing-md);
			border-bottom: 1px solid var(--gray-200);
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			gap: var(--spacing-md);
		}

		.qa-question {
			font-weight: 600;
			color: var(--gray-800);
			flex: 1;
		}

		.qa-meta {
			display: flex;
			gap: var(--spacing-sm);
			align-items: center;
			flex-shrink: 0;
		}

		.qa-badge {
			padding: 2px 8px;
			border-radius: var(--radius-sm);
			font-size: 0.75rem;
			font-weight: 500;
		}

		.qa-badge.difficulty-1 { background: var(--success-light); color: var(--success-dark); }
		.qa-badge.difficulty-2 { background: var(--info-light); color: var(--info-dark); }
		.qa-badge.difficulty-3 { background: var(--warning-light); color: var(--warning-dark); }
		.qa-badge.difficulty-4 { background: var(--danger-light); color: var(--danger-dark); }
		.qa-badge.difficulty-5 { background: var(--purple-light); color: var(--purple-dark); }

		.qa-badge.common { background: var(--primary-light); color: var(--primary-dark); }

		.qa-item-body {
			padding: var(--spacing-md);
		}

		.qa-answer {
			color: var(--gray-700);
			margin-bottom: var(--spacing-md);
		}

		.qa-steps {
			margin-bottom: var(--spacing-md);
		}

		.qa-steps-title {
			font-weight: 600;
			color: var(--gray-800);
			margin-bottom: var(--spacing-sm);
		}

		.qa-steps-list {
			list-style: none;
			padding: 0;
			counter-reset: step-counter;
		}

		.qa-steps-list li {
			padding: var(--spacing-xs) 0;
			color: var(--gray-700);
			position: relative;
			padding-left: var(--spacing-lg);
		}

		.qa-steps-list li:before {
			content: counter(step-counter);
			counter-increment: step-counter;
			position: absolute;
			left: 0;
			top: var(--spacing-xs);
			background: var(--primary);
			color: var(--white);
			width: 20px;
			height: 20px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 0.75rem;
			font-weight: 600;
		}

		.qa-item-footer {
			padding: var(--spacing-md);
			border-top: 1px solid var(--gray-200);
			background: var(--gray-50);
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 0.875rem;
			color: var(--gray-600);
		}

		.qa-list-controls {
			display: flex;
			gap: var(--spacing-sm);
			align-items: center;
		}

		/* 导出下拉菜单样式 */
		.export-dropdown {
			position: relative;
			display: inline-block;
		}

		.dropdown-toggle::after {
			content: '';
			display: inline-block;
			margin-left: var(--spacing-xs);
			vertical-align: 0.255em;
			border-top: 0.3em solid;
			border-right: 0.3em solid transparent;
			border-bottom: 0;
			border-left: 0.3em solid transparent;
		}

		.export-dropdown-menu {
			position: absolute;
			top: 100%;
			right: 0;
			z-index: 1000;
			display: none;
			min-width: 200px;
			padding: var(--spacing-xs) 0;
			margin: 2px 0 0;
			background-color: var(--white);
			border: 1px solid var(--gray-200);
			border-radius: var(--radius-md);
			box-shadow: var(--shadow-lg);
		}

		.export-dropdown.active .export-dropdown-menu {
			display: block;
		}

		.export-option {
			display: block;
			width: 100%;
			padding: var(--spacing-sm) var(--spacing-md);
			color: var(--gray-700);
			text-decoration: none;
			white-space: nowrap;
			transition: all 0.2s ease;
		}

		.export-option:hover {
			background-color: var(--gray-50);
			color: var(--primary);
			text-decoration: none;
		}

		.export-option i {
			width: 16px;
			margin-right: var(--spacing-sm);
		}

		.export-divider {
			height: 1px;
			margin: var(--spacing-xs) 0;
			background-color: var(--gray-200);
		}

		/* 分页样式 */
		.qa-pagination-info {
			padding: var(--spacing-sm) 0;
			color: var(--gray-600);
			font-size: 0.875rem;
			border-bottom: 1px solid var(--gray-200);
			margin-bottom: var(--spacing-md);
		}

		.qa-pagination {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: var(--spacing-xs);
			padding: var(--spacing-lg) 0;
			border-top: 1px solid var(--gray-200);
			margin-top: var(--spacing-md);
		}

		.pagination-btn {
			padding: var(--spacing-xs) var(--spacing-sm);
			border: 1px solid var(--gray-300);
			background: var(--white);
			color: var(--gray-700);
			border-radius: var(--radius-sm);
			cursor: pointer;
			transition: all 0.2s ease;
			font-size: 0.875rem;
			min-width: 36px;
			height: 36px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.pagination-btn:hover {
			background: var(--gray-50);
			border-color: var(--primary);
			color: var(--primary);
		}

		.pagination-btn.active {
			background: var(--primary);
			border-color: var(--primary);
			color: var(--white);
		}

		.pagination-btn:disabled {
			opacity: 0.5;
			cursor: not-allowed;
		}

		.pagination-ellipsis {
			padding: var(--spacing-xs);
			color: var(--gray-500);
		}

		/* 高并发处理状态样式 */
		.processing-status-panel {
			position: fixed;
			top: 20px;
			right: 20px;
			width: 350px;
			background: var(--white);
			border: 1px solid var(--gray-200);
			border-radius: var(--radius-lg);
			box-shadow: var(--shadow-lg);
			z-index: 1000;
			display: none;
		}

		.processing-status-panel.active {
			display: block;
		}

		.status-panel-header {
			padding: var(--spacing-md);
			border-bottom: 1px solid var(--gray-200);
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: var(--primary);
			color: var(--white);
			border-radius: var(--radius-lg) var(--radius-lg) 0 0;
		}

		.status-panel-body {
			padding: var(--spacing-md);
			max-height: 400px;
			overflow-y: auto;
		}

		.progress-info {
			margin-bottom: var(--spacing-md);
		}

		.progress-bar {
			width: 100%;
			height: 8px;
			background: var(--gray-200);
			border-radius: var(--radius-sm);
			overflow: hidden;
			margin-bottom: var(--spacing-sm);
		}

		.progress-fill {
			height: 100%;
			background: var(--primary);
			transition: width 0.3s ease;
		}

		.progress-text {
			font-size: 0.875rem;
			color: var(--gray-600);
			display: flex;
			justify-content: space-between;
		}

		.engine-status {
			display: flex;
			gap: var(--spacing-sm);
			margin-bottom: var(--spacing-md);
		}

		.engine-badge {
			padding: var(--spacing-xs) var(--spacing-sm);
			border-radius: var(--radius-sm);
			font-size: 0.75rem;
			font-weight: 500;
		}

		.engine-badge.kimi {
			background: var(--primary-light);
			color: var(--primary-dark);
		}

		.engine-badge.gemini {
			background: var(--success-light);
			color: var(--success-dark);
		}

		.engine-badge.offline {
			background: var(--gray-light);
			color: var(--gray-dark);
		}

		.file-status-list {
			max-height: 200px;
			overflow-y: auto;
		}

		.file-status-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: var(--spacing-xs) 0;
			border-bottom: 1px solid var(--gray-100);
		}

		.file-status-item:last-child {
			border-bottom: none;
		}

		.file-name {
			flex: 1;
			font-size: 0.875rem;
			color: var(--gray-700);
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			margin-right: var(--spacing-sm);
		}

		.file-status-badge {
			padding: 2px 6px;
			border-radius: var(--radius-sm);
			font-size: 0.75rem;
			font-weight: 500;
		}

		.file-status-badge.pending {
			background: var(--gray-light);
			color: var(--gray-dark);
		}

		.file-status-badge.processing {
			background: var(--warning-light);
			color: var(--warning-dark);
		}

		.file-status-badge.completed {
			background: var(--success-light);
			color: var(--success-dark);
		}

		.file-status-badge.failed {
			background: var(--danger-light);
			color: var(--danger-dark);
		}

		.file-status-badge.skipped {
			background: var(--info-light);
			color: var(--info-dark);
		}

		.status-panel-actions {
			padding: var(--spacing-md);
			border-top: 1px solid var(--gray-200);
			display: flex;
			gap: var(--spacing-sm);
		}

		.btn-sm {
			padding: var(--spacing-xs) var(--spacing-sm);
			font-size: 0.875rem;
		}

		@media (max-width: 768px) {
			.charts {
				grid-template-columns: 1fr;
			}

			.tabs-nav {
				flex-direction: column;
			}

			.tab-btn {
				border-right: none;
				border-bottom: 1px solid var(--gray-200);
			}

			.tab-btn:last-child {
				border-bottom: none;
			}

			.qa-toolbar {
				flex-direction: column;
				align-items: stretch;
			}

			.search-box {
				min-width: auto;
			}

			.filter-controls {
				justify-content: space-between;
			}

			.qa-stats-grid {
				grid-template-columns: 1fr;
			}
		}
	</style>
</head>
<body>
	<!-- 头部 -->
	<header class="header">
		<div class="container">
			<div class="header-content">
				<h1 class="app-title">
					<i class="fas fa-chart-line"></i>
					GoMyHire 对话分析平台 (单文件版)
				</h1>
			</div>
		</div>
	</header>

	<!-- 主内容区域 -->
	<main class="main">
		<div class="container">
			<!-- 标签页导航 -->
			<div class="tabs-container">
				<div class="tabs-nav">
					<button class="tab-btn active" data-tab="analysis">
						<i class="fas fa-chart-line"></i>
						数据分析
					</button>
					<button class="tab-btn" data-tab="reports">
						<i class="fas fa-file-alt"></i>
						详细报告
					</button>
					<button class="tab-btn" data-tab="qa-dataset">
						<i class="fas fa-question-circle"></i>
						问答题集
					</button>
				</div>
			</div>

			<!-- 数据分析标签页内容 -->
			// 内联 LLM 函数已迁移至 llm.js，通过顶部 import 使用
							<div class="stat-content">
								<div class="stat-value" id="main-drivers-count">0</div>
								<div class="stat-label">司机数量</div>
							</div>
						</div>
						<div class="stat-item">
							<div class="stat-icon"><i class="fas fa-headset"></i></div>
							<div class="stat-content">
								<div class="stat-value" id="main-agents-count">0</div>
								<div class="stat-label">客服数量</div>
							</div>
						</div>
						<div class="stat-item">
							<div class="stat-icon"><i class="fas fa-book"></i></div>
							<div class="stat-content">
								<div class="stat-value" id="main-knowledge-count">0</div>
								<div class="stat-label">知识库条目</div>
							</div>
						</div>
						<div class="stat-item">
							<div class="stat-icon"><i class="fas fa-question-circle"></i></div>
							<div class="stat-content">
								<div class="stat-value" id="main-qa-count">0</div>
								<div class="stat-label">问答题集</div>
							</div>
						</div>
						<!-- 质量指标 -->
						<div class="stat-item">
							<div class="stat-icon"><i class="fas fa-star"></i></div>
							<div class="stat-content">
								<div class="stat-value" id="main-avg-satisfaction">0</div>
								<div class="stat-label">平均满意度</div>
							</div>
						</div>
						<div class="stat-item">
							<div class="stat-icon"><i class="fas fa-check-circle"></i></div>
							<div class="stat-content">
								<div class="stat-value" id="main-avg-effectiveness">0</div>
								<div class="stat-label">平均有效性</div>
							</div>
						</div>
						<div class="stat-item">
							<div class="stat-icon"><i class="fas fa-clock"></i></div>
							<div class="stat-content">
								<div class="stat-value" id="main-avg-response-time">0</div>
								<div class="stat-label">平均响应时间</div>
							</div>
						</div>
						<div class="stat-item">
							<div class="stat-icon"><i class="fas fa-thumbs-up"></i></div>
							<div class="stat-content">
								<div class="stat-value" id="main-common-qa-count">0</div>
								<div class="stat-label">通用问答</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 实时结果预览 -->
			<div class="container" style="margin-top: var(--spacing-xl);">
				<div class="card">
					<div class="card-header">
						<h2><i class="fas fa-table"></i> 实时数据预览</h2>
						<div style="display: flex; gap: var(--spacing-sm); align-items: center;">
							<span class="text-muted" id="results-count">0 条记录</span>
							<button id="clear-results-btn" class="btn-sm btn-outline-danger" style="display: none;">
								<i class="fas fa-trash"></i> 清空
							</button>
						</div>
					</div>
					<div class="results-table-container">
						<table class="results-table" id="results-table">
							<thead>
								<tr>
									<th>状态</th>
									<th>文件名</th>
									<th>司机问题</th>
									<th>客服</th>
									<th>有效性</th>
									<th>满意度</th>
									<th>响应时间</th>
									<th>问题标签</th>
								</tr>
							</thead>
							<tbody id="results-tbody">
								<tr>
									<td colspan="8" class="text-muted" style="text-align: center; padding: var(--spacing-xl);">
										暂无数据，开始分析后将实时显示结果
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>

			<!-- 数据可视化图表区域 -->
			<div class="container charts-section">
				<div class="card">
					<div class="card-header">
						<h2><i class="fas fa-chart-bar"></i> 数据可视化</h2>
					</div>
					<div class="charts">
						<!-- 问题分布图表 -->
						<div class="chart-container">
							<div id="chart-questions" class="chart"></div>
							<div class="chart-preview">
								<h4>问题分布详情</h4>
								<div class="chart-table-container">
									<table class="chart-preview-table" id="questions-preview-table">
										<thead>
											<tr>
												<th>问题类别</th>
												<th>数量</th>
												<th>占比</th>
												<th>最新问题</th>
											</tr>
										</thead>
										<tbody id="questions-preview-tbody">
											<tr><td colspan="4" class="no-data">暂无数据</td></tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<!-- 有效性图表 -->
						<div class="chart-container">
							<div id="chart-effectiveness" class="chart"></div>
							<div class="chart-preview">
								<h4>有效性分析</h4>
								<div class="chart-table-container">
									<table class="chart-preview-table" id="effectiveness-preview-table">
										<thead>
											<tr>
												<th>司机</th>
												<th>有效性</th>
												<th>响应时间</th>
												<th>问题数</th>
											</tr>
										</thead>
										<tbody id="effectiveness-preview-tbody">
											<tr><td colspan="4" class="no-data">暂无数据</td></tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<!-- 满意度图表 -->
						<div class="chart-container">
							<div id="chart-satisfaction" class="chart"></div>
							<div class="chart-preview">
								<h4>满意度统计</h4>
								<div class="chart-table-container">
									<table class="chart-preview-table" id="satisfaction-preview-table">
										<thead>
											<tr>
												<th>司机</th>
												<th>满意度</th>
												<th>客服</th>
												<th>最新评价</th>
											</tr>
										</thead>
										<tbody id="satisfaction-preview-tbody">
											<tr><td colspan="4" class="no-data">暂无数据</td></tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<!-- 知识库图表 -->
						<div class="chart-container">
							<div id="chart-knowledge" class="chart"></div>
							<div class="chart-preview">
								<h4>知识库条目</h4>
								<div class="chart-table-container">
									<table class="chart-preview-table" id="knowledge-preview-table">
										<thead>
											<tr>
												<th>类别</th>
												<th>知识点</th>
												<th>复杂度</th>
												<th>来源文件</th>
											</tr>
										</thead>
										<tbody id="knowledge-preview-tbody">
											<tr><td colspan="4" class="no-data">暂无数据</td></tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<!-- 问答题集难度分布图表 -->
						<div class="chart-container">
							<div id="chart-qa-difficulty" class="chart"></div>
							<div class="chart-preview">
								<h4>问答难度分布</h4>
								<div class="chart-table-container">
									<table class="chart-preview-table" id="qa-difficulty-preview-table">
										<thead>
											<tr>
												<th>难度等级</th>
												<th>问答数量</th>
												<th>占比</th>
												<th>平均频次</th>
											</tr>
										</thead>
										<tbody id="qa-difficulty-preview-tbody">
											<tr><td colspan="4" class="no-data">暂无数据</td></tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<!-- 问答题集标签分布图表 -->
						<div class="chart-container">
							<div id="chart-qa-tags" class="chart"></div>
							<div class="chart-preview">
								<h4>问答标签分布</h4>
								<div class="chart-table-container">
									<table class="chart-preview-table" id="qa-tags-preview-table">
										<thead>
											<tr>
												<th>标签</th>
												<th>问答数量</th>
												<th>占比</th>
												<th>通用问题数</th>
											</tr>
										</thead>
										<tbody id="qa-tags-preview-tbody">
											<tr><td colspan="4" class="no-data">暂无数据</td></tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			</div> <!-- 结束数据分析标签页 -->

			<!-- 详细报告标签页内容 -->
			<div id="tab-reports" class="tab-content">
				<div class="card">
					<div class="card-header">
						<h2><i class="fas fa-file-alt"></i> 详细分析报告</h2>
						<button id="generate-report-btn" class="btn-primary">
							<i class="fas fa-chart-bar"></i>
							生成详细报告
						</button>
					</div>
					<div class="card-body">
						<!-- 报告配置区域 -->
						<div id="report-config-section" class="report-section">
							<h3><i class="fas fa-cog"></i> 报告配置</h3>
							<div class="config-grid">
								<div class="config-item">
									<label>文件选择</label>
									<div id="file-selection-area" class="file-selection">
										<div class="selection-summary">
											<span id="selected-files-count">0</span> 个文件已选择
											<button id="select-files-btn" class="btn-sm btn-outline-primary">选择文件</button>
										</div>
									</div>
								</div>
								<div class="config-item">
									<label>时间范围</label>
									<div class="date-range">
										<input type="date" id="report-start-date" class="form-control">
										<span>至</span>
										<input type="date" id="report-end-date" class="form-control">
									</div>
								</div>
								<div class="config-item">
									<label>报告类型</label>
									<select id="report-type" class="form-control">
										<option value="comprehensive">综合报告</option>
										<option value="summary">摘要报告</option>
										<option value="detailed">详细报告</option>
									</select>
								</div>
								<div class="config-item">
									<label>导出格式</label>
									<div class="format-checkboxes">
										<label><input type="checkbox" id="export-html" checked> HTML</label>
										<label><input type="checkbox" id="export-pdf"> PDF</label>
										<label><input type="checkbox" id="export-word"> Word</label>
									</div>
								</div>
							</div>
						</div>

						<!-- 报告生成进度区域 -->
						<div id="report-progress-section" class="report-section" style="display: none;">
							<h3><i class="fas fa-spinner fa-spin"></i> 生成进度</h3>
							<div class="progress-container">
								<div class="progress-bar">
									<div id="report-progress-fill" class="progress-fill"></div>
								</div>
								<div class="progress-info">
									<span id="report-progress-text">准备中...</span>
									<span id="report-progress-percentage">0%</span>
								</div>
								<div class="progress-details">
									<div id="report-current-step">正在初始化...</div>
									<div id="report-estimated-time">预计完成时间: --</div>
								</div>
								<button id="cancel-report-btn" class="btn-sm btn-danger">取消生成</button>
							</div>
						</div>

						<!-- 报告预览区域 -->
						<div id="report-preview-section" class="report-section" style="display: none;">
							<h3><i class="fas fa-eye"></i> 报告预览</h3>
							<div class="report-actions">
								<button id="edit-report-btn" class="btn-sm btn-outline-primary">
									<i class="fas fa-edit"></i> 编辑
								</button>
								<button id="export-report-btn" class="btn-sm btn-primary">
									<i class="fas fa-download"></i> 导出
								</button>
								<button id="save-template-btn" class="btn-sm btn-outline-secondary">
									<i class="fas fa-save"></i> 保存为模板
								</button>
							</div>
							<div id="report-preview-content" class="report-preview">
								<!-- 报告内容将在这里显示 -->
							</div>
						</div>

						<!-- 报告历史记录区域 -->
						<div id="report-history-section" class="report-section">
							<h3><i class="fas fa-history"></i> 历史报告</h3>
							<div id="report-history-list" class="history-list">
								<div class="empty-state">
									<i class="fas fa-file-alt"></i>
									<p>暂无历史报告</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 问答题集标签页内容 -->
			<div id="tab-qa-dataset" class="tab-content">
				<div class="qa-dataset-container">
					<!-- 工具栏 -->
					<div class="card">
						<div class="card-header">
							<h2><i class="fas fa-question-circle"></i> 问答题集管理</h2>
							<div class="qa-toolbar">
								<div class="search-box">
									<input type="text" id="qa-search" placeholder="搜索问题或答案..." class="form-control">
									<i class="fas fa-search"></i>
								</div>
								<div class="filter-controls">
									<select id="qa-difficulty-filter" class="form-control">
										<option value="">所有难度</option>
										<option value="1">简单</option>
										<option value="2">一般</option>
										<option value="3">中等</option>
										<option value="4">困难</option>
										<option value="5">复杂</option>
									</select>
									<select id="qa-common-filter" class="form-control">
										<option value="">所有类型</option>
										<option value="true">通用问题</option>
										<option value="false">特殊问题</option>
									</select>
									<button id="qa-optimize-btn" class="btn-primary" type="button" title="智能优化：标签化处理和去重优化">
										<i class="fas fa-magic"></i> 智能优化
									</button>
									<div class="export-dropdown">
										<button id="qa-export-btn" class="btn-primary dropdown-toggle" type="button">
											<i class="fas fa-download"></i> 导出数据
										</button>
										<div class="export-dropdown-menu" id="export-dropdown-menu">
											<a href="#" class="export-option" data-format="csv" data-type="all">
												<i class="fas fa-file-csv"></i> 导出全部数据 (CSV)
											</a>
											<a href="#" class="export-option" data-format="csv" data-type="filtered">
												<i class="fas fa-filter"></i> 导出筛选数据 (CSV)
											</a>
											<a href="#" class="export-option" data-format="json" data-type="all">
												<i class="fas fa-file-code"></i> 导出全部数据 (JSON)
											</a>
											<a href="#" class="export-option" data-format="json" data-type="filtered">
												<i class="fas fa-filter"></i> 导出筛选数据 (JSON)
											</a>
											<div class="export-divider"></div>
											<a href="#" class="export-option" data-format="template">
												<i class="fas fa-file-alt"></i> 下载导入模板
											</a>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- 统计信息 -->
					<div class="card">
						<div class="card-header">
							<h3><i class="fas fa-chart-pie"></i> 统计概览</h3>
						</div>
						<div class="qa-stats-grid">
							<div class="stat-item">
								<div class="stat-icon"><i class="fas fa-question"></i></div>
								<div class="stat-content">
									<div class="stat-value" id="qa-total-count">0</div>
									<div class="stat-label">总问答数</div>
								</div>
							</div>
							<div class="stat-item">
								<div class="stat-icon"><i class="fas fa-star"></i></div>
								<div class="stat-content">
									<div class="stat-value" id="qa-common-count">0</div>
									<div class="stat-label">通用问题</div>
								</div>
							</div>
							<div class="stat-item">
								<div class="stat-icon"><i class="fas fa-chart-line"></i></div>
								<div class="stat-content">
									<div class="stat-value" id="qa-avg-difficulty">0</div>
									<div class="stat-label">平均难度</div>
								</div>
							</div>
							<div class="stat-item">
								<div class="stat-icon"><i class="fas fa-sync"></i></div>
								<div class="stat-content">
									<div class="stat-value" id="qa-avg-frequency">0</div>
									<div class="stat-label">平均频次</div>
								</div>
							</div>
						</div>
					</div>

					<!-- 问答列表 -->
					<div class="card">
						<div class="card-header">
							<h3><i class="fas fa-list"></i> 问答列表</h3>
							<div class="qa-list-controls">
								<select id="qa-sort-by" class="form-control">
									<option value="frequency">按频次排序</option>
									<option value="difficulty">按难度排序</option>
									<option value="created">按创建时间</option>
									<option value="updated">按更新时间</option>
								</select>
							</div>
						</div>
						<div id="qa-list-container" class="qa-list-container">
							<div class="qa-empty-state">
								<i class="fas fa-question-circle"></i>
								<p>暂无问答数据</p>
								<p class="text-muted">上传对话文件进行分析后，系统会自动提取问答数据</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</main>

	<!-- JavaScript代码 - 集成版本 -->
	<script type="module">
	import { MAX_CONCURRENCY, CSV_MIME, defaultHeaders } from './constants.js';
	import { STORAGE_KEYS, LEGACY_ENHANCED_KEYS, StorageManager } from './storage.js';
	import { initializeCharts, setupCharts, updateChartsData } from './charts.js';
	import { evaluateConversationWithKimi, callKimiAPI, parseKimiResponse, repairAndParseJSON, processAnalysisResult as processLLMAnalysisResult, loadApiKey } from './llm.js';
	import { parseConversationFile, parseTxtContent, parseTimestamp } from './parser.js';
		// ==================== CONSTANTS.JS ====================
		/**
		 * constants.js - 全局常量配置文件
		 * @CONFIG_FILE 全局常量配置文件
		 * 创建日期: 2024-06-13

					metric.resolutionRate || 0,
					metric.effectiveness || 0,
					metric.efficiency || 0,
					metric.satisfaction || 0,
					metric.attitude || 0,
					metric.complexity || 1,
					metric.priority || 1,
					metric.question || '',
					metric.questionBrief || '',
					Array.isArray(metric.questionTags) ? metric.questionTags.join(';') : '',
					metric.timestamp || ''
				]);

				return Papa.unparse({
					fields: headers,
					data: rows
				});
			}

			// @UTIL 导出司机数据CSV
			exportDriversCSV() {
				const headers = ['司机姓名', '对话次数', '平均满意度', '平均有效性', '平均解决率', '问题类型分布', '最后更新时间'];
				const rows = Object.entries(this.data.drivers).map(([name, data]) => [
					name,
					data.conversationCount || 0,
					Math.round(data.avgSatisfaction || 0),
					Math.round(data.avgEffectiveness || 0),
					Math.round(data.avgResolutionRate || 0),
					data.questionTypes ? Object.keys(data.questionTypes).join(';') : '',
					data.lastUpdated || ''
				]);

				return Papa.unparse({
					fields: headers,
					data: rows
				});
			}

			// @UTIL 导出客服数据CSV
			exportSupportAgentsCSV() {
				const headers = ['客服姓名', '处理案例数', '平均响应时间', '平均解决率', '平均有效性', '平均效率', '平均态度', '擅长标签', '最后更新时间'];
				const rows = Object.entries(this.data.supportAgents).map(([name, data]) => [
					name,
					data.totalCases || 0,
					Math.round(data.avgResponseTime || 0),
					Math.round(data.avgResolutionRate || 0),
					Math.round(data.avgEffectiveness || 0),
					Math.round(data.avgEfficiency || 0),
					Math.round(data.avgAttitude || 0),
					data.handledTags ? Object.keys(data.handledTags).join(';') : '',
					data.lastUpdated || ''
				]);

				return Papa.unparse({
					fields: headers,
					data: rows
				});
			}

			// @UTIL 导出问题标签CSV
			exportQuestionTagsCSV() {
				const headers = ['标签', '出现次数'];
				const rows = Object.entries(this.data.questionTags).map(([tag, count]) => [
					tag,
					count
				]);

				return Papa.unparse({
					fields: headers,
					data: rows
				});
			}

			// @UTIL 导出详细问题CSV
			exportDetailedQuestionsCSV() {
				const headers = ['问题ID', '详细问题', '问题简述', '标签', '司机姓名', '客服姓名', '解决率', '有效性', '复杂度', '优先级', '知识点', '来源文件', '创建时间'];
				const rows = this.data.detailedQuestions.map(item => [
					item.id || '',
					item.question || '',
					item.questionBrief || '',
					Array.isArray(item.tags) ? item.tags.join(';') : '',
					item.driverName || '',
					item.supportAgent || '',
					item.resolutionRate || 0,
					item.effectiveness || 0,
					item.complexity || 1,
					item.priority || 1,
					item.knowledge || '',
					item.source || '',
					item.createdAt || ''
				]);

				return Papa.unparse({
					fields: headers,
					data: rows
				});
			}

			// @SERVICE 获取统计摘要
			getStatsSummary() {
				return {
					totalDrivers: Object.keys(this.data.drivers).length,
					totalSupportAgents: Object.keys(this.data.supportAgents).length,
					totalKnowledge: this.data.knowledge.length,
					totalMetrics: this.data.metrics.length,
					totalDetailedQuestions: this.data.detailedQuestions.length,
					totalCategories: Object.keys(this.data.questionCategories).length,
					totalTags: Object.keys(this.data.questionTags).length,
					storageInfo: this.storage.getStorageInfo()
				};
			}

			// @SERVICE 更新客服数据
			updateSupportAgentData(agentName, agentData) {
				this.data.supportAgents[agentName] = {
					...this.data.supportAgents[agentName],
					...agentData
				};

				if (this.data.settings.autoSave) {
					this.storage.save(STORAGE_KEYS.SUPPORT_AGENTS, this.data.supportAgents);
				}
			}

			// @SERVICE 更新问题标签统计
			updateQuestionTags(tags) {
				Object.entries(tags).forEach(([tag, count]) => {
					this.data.questionTags[tag] =
						(this.data.questionTags[tag] || 0) + count;
				});

				if (this.data.settings.autoSave) {
					this.storage.save(STORAGE_KEYS.QUESTION_TAGS, this.data.questionTags);
				}
			}

			// @SERVICE 添加详细问题记录
			addDetailedQuestion(questionData) {
				this.data.detailedQuestions.push({
					...questionData,
					createdAt: questionData.createdAt || new Date().toISOString()
				});

				if (this.data.settings.autoSave) {
					this.storage.save(STORAGE_KEYS.DETAILED_QUESTIONS, this.data.detailedQuestions);
				}
			}

			// @SERVICE 批量添加详细问题记录
			addDetailedQuestions(questionsArray) {
				questionsArray.forEach(question => {
					this.data.detailedQuestions.push({
						...question,
						createdAt: question.createdAt || new Date().toISOString()
					});
				});

				if (this.data.settings.autoSave) {
					this.storage.save(STORAGE_KEYS.DETAILED_QUESTIONS, this.data.detailedQuestions);
				}
			}

			// @SERVICE 添加问答题集数据
			addQAItem(qaData) {
				if (!qaData || !qaData.question || !qaData.answer) {
					console.warn('问答数据无效，跳过添加');
					return false;
				}

				// 生成唯一ID
				const qaId = this.generateQAId(qaData.question);

				// 检查是否已存在相似问题
				const existingIndex = this.findSimilarQA(qaData.question);

				if (existingIndex >= 0) {
					// 更新现有问答
					this.data.qaDataset[existingIndex] = {
						...this.data.qaDataset[existingIndex],
						...qaData,
						id: this.data.qaDataset[existingIndex].id, // 保持原有ID
						frequency: (this.data.qaDataset[existingIndex].frequency || 0) + 1,
						lastUpdated: new Date().toISOString(),
						sources: this.mergeSources(this.data.qaDataset[existingIndex].sources, qaData.sources)
					};
					console.log(`✅ 更新问答题集: ${qaData.question.substring(0, 50)}...`);
				} else {
					// 添加新问答
					const newQA = {
						id: qaId,
						...qaData,
						frequency: 1,
						createdAt: new Date().toISOString(),
						lastUpdated: new Date().toISOString(),
						sources: qaData.sources || []
					};
					this.data.qaDataset.push(newQA);
					console.log(`✅ 新增问答题集: ${qaData.question.substring(0, 50)}...`);
				}

				if (this.data.settings.autoSave) {
					this.storage.save(STORAGE_KEYS.QA_DATASET, this.data.qaDataset);
				}

				// 检查是否需要触发优化
				this.checkAndTriggerOptimization();

				return true;
			}

			// @SERVICE 批量添加问答题集数据
			addQAItems(qaArray) {
				let addedCount = 0;
				let updatedCount = 0;

				qaArray.forEach(qaData => {
					if (this.addQAItem(qaData)) {
						addedCount++;
					}
				});

				console.log(`📊 问答题集处理完成: 新增/更新 ${addedCount} 条`);
				return { addedCount, updatedCount };
			}

			// @SERVICE 检查并触发优化
			async checkAndTriggerOptimization() {
				if (!qaOptimizationManager) return;

				const qaDatasetLength = this.data.qaDataset ? this.data.qaDataset.length : 0;

				// 检查是否需要优化（每100条触发一次）
				if (qaOptimizationManager.shouldOptimize(qaDatasetLength) &&
					qaDatasetLength % 100 === 0 &&
					!qaOptimizationManager.isProcessing) {

					console.log(`[自动优化] 问答数据达到 ${qaDatasetLength} 条，触发自动优化`);

					try {
						// 显示优化提示
						this.showOptimizationNotification(qaDatasetLength);

						// 执行优化（异步）
						setTimeout(async () => {
							await this.performAutoOptimization();
						}, 1000);

					} catch (error) {
						console.error('[自动优化] 触发失败:', error);
					}
				}
			}

			// @SERVICE 显示优化提示
			showOptimizationNotification(count) {
				// 创建提示消息
				const notification = document.createElement('div');
				notification.className = 'optimization-notification';
				notification.style.cssText = `
					position: fixed;
					top: 20px;
					right: 20px;
					background: #007bff;
					color: white;
					padding: 15px 20px;
					border-radius: 8px;
					box-shadow: 0 4px 12px rgba(0,0,0,0.15);
					z-index: 1000;
					max-width: 300px;
					font-size: 14px;
					line-height: 1.4;
				`;

				notification.innerHTML = `
					<div style="display: flex; align-items: center; gap: 10px;">
						<i class="fas fa-magic" style="font-size: 16px;"></i>
						<div>
							<div style="font-weight: 600;">自动优化启动</div>
							<div style="font-size: 12px; opacity: 0.9;">正在优化 ${count} 条问答数据...</div>
						</div>
					</div>
				`;

				document.body.appendChild(notification);

				// 3秒后自动移除
				setTimeout(() => {
					if (notification.parentNode) {
						notification.parentNode.removeChild(notification);
					}
				}, 3000);
			}

			// @SERVICE 执行自动优化
			async performAutoOptimization() {
				if (!qaOptimizationManager || !this.data.qaDataset) return;

				try {
					console.log('[自动优化] 开始执行问答数据优化');

					const result = await qaOptimizationManager.optimizeQADataset(
						this.data.qaDataset,
						{
							progressCallback: (progress) => {
								console.log(`[自动优化] ${progress.stage}: ${progress.step} ${progress.percentage}%`);
							}
						}
					);

					if (result.success) {
						// 更新数据
						this.data.qaDataset = result.optimized.items;
						this.saveData();

						// 显示优化结果
						this.showOptimizationResult(result);

						// 更新UI
						if (typeof updateQADatasetDisplay === 'function') {
							updateQADatasetDisplay();
						}

						console.log(`[自动优化] 优化完成: ${result.original.count} -> ${result.optimized.count} (-${result.statistics.reductionRate}%)`);
					} else {
						console.error('[自动优化] 优化失败:', result.error);
					}

				} catch (error) {
					console.error('[自动优化] 执行失败:', error);
				}
			}

			// @SERVICE 显示优化结果
			showOptimizationResult(result) {
				const notification = document.createElement('div');
				notification.className = 'optimization-result-notification';
				notification.style.cssText = `
					position: fixed;
					top: 20px;
					right: 20px;
					background: #28a745;
					color: white;
					padding: 15px 20px;
					border-radius: 8px;
					box-shadow: 0 4px 12px rgba(0,0,0,0.15);
					z-index: 1000;
					max-width: 350px;
					font-size: 14px;
					line-height: 1.4;
				`;

				notification.innerHTML = `
					<div style="display: flex; align-items: center; gap: 10px;">
						<i class="fas fa-check-circle" style="font-size: 16px;"></i>
						<div>
							<div style="font-weight: 600;">优化完成</div>
							<div style="font-size: 12px; opacity: 0.9;">
								${result.original.count} → ${result.optimized.count} 条
								(减少 ${result.statistics.reductionRate}%)
							</div>
							<div style="font-size: 12px; opacity: 0.9;">
								用时: ${Math.round(result.duration / 1000)}秒
							</div>
						</div>
					</div>
				`;

				document.body.appendChild(notification);

				// 5秒后自动移除
				setTimeout(() => {
					if (notification.parentNode) {
						notification.parentNode.removeChild(notification);
					}
				}, 5000);
			}

			// @UTIL 生成问答ID
			generateQAId(question) {
				// 使用问题的哈希值作为ID
				const hash = this.simpleHash(question.toLowerCase().trim());
				return `qa_${hash}_${Date.now()}`;
			}

			// @UTIL 简单哈希函数
			simpleHash(str) {
				let hash = 0;
				for (let i = 0; i < str.length; i++) {
					const char = str.charCodeAt(i);
					hash = ((hash << 5) - hash) + char;
					hash = hash & hash; // 转换为32位整数
				}
				return Math.abs(hash).toString(36);
			}

			// @UTIL 查找相似问答
			findSimilarQA(question) {
				const normalizedQuestion = question.toLowerCase().trim();

				return this.data.qaDataset.findIndex(qa => {
					const existingQuestion = qa.question.toLowerCase().trim();

					// 完全匹配
					if (existingQuestion === normalizedQuestion) {
						return true;
					}

					// 相似度匹配（简单的关键词匹配）
					const similarity = this.calculateSimilarity(existingQuestion, normalizedQuestion);
					return similarity > 0.8; // 80%相似度阈值
				});
			}

			// @UTIL 计算文本相似度
			calculateSimilarity(str1, str2) {
				const words1 = str1.split(/\s+/);
				const words2 = str2.split(/\s+/);

				const commonWords = words1.filter(word => words2.includes(word));
				const totalWords = new Set([...words1, ...words2]).size;

				return commonWords.length / totalWords;
			}

			// @UTIL 合并数据源
			mergeSources(existingSources = [], newSources = []) {
				const allSources = [...existingSources, ...newSources];
				return [...new Set(allSources)]; // 去重
			}

			// @SERVICE 获取问答题集数据
			getQADataset(filters = {}) {
				let dataset = [...this.data.qaDataset];

				// 按标签筛选
				if (filters.tags && filters.tags.length > 0) {
					dataset = dataset.filter(qa =>
						qa.tags && qa.tags.some(tag => filters.tags.includes(tag))
					);
				}

				// 按难度筛选
				if (filters.difficulty) {
					dataset = dataset.filter(qa => qa.difficulty === filters.difficulty);
				}

				// 按通用性筛选
				if (filters.isCommon !== undefined) {
					dataset = dataset.filter(qa => qa.isCommon === filters.isCommon);
				}

				// 按关键词搜索
				if (filters.keyword) {
					const keyword = filters.keyword.toLowerCase();
					dataset = dataset.filter(qa =>
						qa.question.toLowerCase().includes(keyword) ||
						qa.answer.toLowerCase().includes(keyword)
					);
				}

				// 排序
				if (filters.sortBy) {
					dataset.sort((a, b) => {
						switch (filters.sortBy) {
							case 'frequency':
								return (b.frequency || 0) - (a.frequency || 0);
							case 'difficulty':
								return (a.difficulty || 1) - (b.difficulty || 1);
							case 'created':
								return new Date(b.createdAt) - new Date(a.createdAt);
							case 'updated':
								return new Date(b.lastUpdated) - new Date(a.lastUpdated);
							default:
								return 0;
						}
					});
				}

				return dataset;
			}

			// @SERVICE 导出问答题集为CSV
			exportQADatasetCSV(filters = {}) {
				const dataset = this.getQADataset(filters);

				if (dataset.length === 0) {
					return null;
				}

				const headers = [
					'ID', '问题', '答案', '操作步骤', '适用场景',
					'难度等级', '是否通用', '标签', '频次',
					'创建时间', '更新时间', '数据源'
				];

				const rows = dataset.map(qa => [
					qa.id || '',
					qa.question || '',
					qa.answer || '',
					Array.isArray(qa.operationSteps) ? qa.operationSteps.join('；') : '',
					qa.applicableScenarios || '',
					qa.difficulty || '',
					qa.isCommon ? '是' : '否',
					Array.isArray(qa.tags) ? qa.tags.join('；') : '',
					qa.frequency || 0,
					qa.createdAt || '',
					qa.lastUpdated || '',
					Array.isArray(qa.sources) ? qa.sources.join('；') : ''
				]);

				return [headers, ...rows].map(row =>
					row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
				).join('\n');
			}

			// @SERVICE 获取问答题集统计信息
			getQADatasetStats() {
				const dataset = this.data.qaDataset;

				if (dataset.length === 0) {
					return {
						total: 0,
						byDifficulty: {},
						byTags: {},
						commonQuestions: 0,
						avgFrequency: 0
					};
				}

				const stats = {
					total: dataset.length,
					byDifficulty: {},
					byTags: {},
					commonQuestions: dataset.filter(qa => qa.isCommon).length,
					avgFrequency: dataset.reduce((sum, qa) => sum + (qa.frequency || 0), 0) / dataset.length
				};

				// 按难度统计
				dataset.forEach(qa => {
					const difficulty = qa.difficulty || 1;
					stats.byDifficulty[difficulty] = (stats.byDifficulty[difficulty] || 0) + 1;
				});

				// 按标签统计
				dataset.forEach(qa => {
					if (qa.tags && Array.isArray(qa.tags)) {
						qa.tags.forEach(tag => {
							stats.byTags[tag] = (stats.byTags[tag] || 0) + 1;
						});
					}
				});

				return stats;
			}
		}

		// 仪表盘元素
		const processedFilesEl = document.getElementById('processed-files');
		const totalMessagesEl = document.getElementById('total-messages');
		const totalQuestionsEl = document.getElementById('total-questions');
		const avgSatisfactionEl = document.getElementById('avg-satisfaction');

		// ==================== PARSER.JS ====================
		/**
		 * parser.js - 文本解析和对话处理服务
		 * @SERVICE 文本解析和对话处理服务
		 * 创建日期: 2024-06-13
		 */

		// @CONSTANTS 问题标签定义
		const QUESTION_TAGS = [
			"薪资福利", "订单派单", "车辆维护", "路线导航", "客户服务",
			"系统操作", "政策咨询", "投诉建议", "技术故障", "紧急求助",
			"培训考核", "其他"
		];

		// @CONSTANTS 文件处理状态
		const FILE_STATUS = {
			PENDING: 'pending',        // 未处理
			PROCESSING: 'processing',  // 处理中
			COMPLETED: 'completed',    // 已完成
			FAILED: 'failed',         // 失败
			SKIPPED: 'skipped'        // 跳过（重复文件）
		};

		// @CONSTANTS LLM引擎类型
		const LLM_ENGINES = {
			KIMI: 'kimi',
			GEMINI: 'gemini',
			AUTO: 'auto'
		};

		// @CONSTANTS 复杂度级别定义
		const COMPLEXITY_LEVELS = {
			1: "简单查询",
			2: "一般操作",
			3: "中等难度",
			4: "复杂问题",
			5: "疑难杂症"
		};

		// ==================== 文件注册表和状态管理 ====================
		/**
		 * @CLASS 文件注册表管理器
		 * 负责文件的唯一标识、状态跟踪和去重处理
		 */
		class FileRegistry {
			constructor() {
				this.storage = new StorageManager();
				this.registry = this.loadRegistry();
			}

			// @SERVICE 加载文件注册表
			loadRegistry() {
				return this.storage.load(STORAGE_KEYS.FILE_REGISTRY, {});
			}

			// @SERVICE 保存文件注册表
			saveRegistry() {
				return this.storage.save(STORAGE_KEYS.FILE_REGISTRY, this.registry);
			}

			// @UTIL 生成文件唯一标识
			generateFileId(file) {
				const content = `${file.name}_${file.size}_${file.lastModified}`;
				return this.simpleHash(content);
			}

			// @UTIL 简单哈希函数
			simpleHash(str) {
				let hash = 0;
				for (let i = 0; i < str.length; i++) {
					const char = str.charCodeAt(i);
					hash = ((hash << 5) - hash) + char;
					hash = hash & hash; // 转换为32位整数
				}
				return Math.abs(hash).toString(36);
			}

			// @SERVICE 注册文件
			registerFile(file, engine = LLM_ENGINES.AUTO) {
				const fileId = this.generateFileId(file);
				const now = new Date().toISOString();

				// 检查是否已存在
				if (this.registry[fileId]) {
					const existing = this.registry[fileId];
					if (existing.status === FILE_STATUS.COMPLETED) {
						console.log(`📋 文件已存在且已完成: ${file.name}`);
						return { fileId, isDuplicate: true, existing };
					}
				}

				// 注册新文件或更新现有文件
				this.registry[fileId] = {
					id: fileId,
					name: file.name,
					size: file.size,
					lastModified: file.lastModified,
					status: FILE_STATUS.PENDING,
					engine: engine,
					registeredAt: now,
					updatedAt: now,
					attempts: 0,
					error: null,
					result: null
				};

				this.saveRegistry();
				console.log(`📝 文件已注册: ${file.name} (ID: ${fileId})`);
				return { fileId, isDuplicate: false };
			}

			// @SERVICE 更新文件状态
			updateFileStatus(fileId, status, data = {}) {
				if (!this.registry[fileId]) {
					console.warn(`⚠️ 文件ID不存在: ${fileId}`);
					return false;
				}

				const file = this.registry[fileId];
				file.status = status;
				file.updatedAt = new Date().toISOString();

				// 更新额外数据
				Object.assign(file, data);

				// 如果是失败状态，增加尝试次数
				if (status === FILE_STATUS.FAILED) {
					file.attempts = (file.attempts || 0) + 1;
				}

				this.saveRegistry();
				console.log(`📊 文件状态更新: ${file.name} -> ${status}`);
				return true;
			}

			// @SERVICE 获取文件状态
			getFileStatus(fileId) {
				return this.registry[fileId] || null;
			}

			// @SERVICE 获取所有文件状态
			getAllFiles() {
				return Object.values(this.registry);
			}

			// @SERVICE 获取特定状态的文件
			getFilesByStatus(status) {
				return Object.values(this.registry).filter(file => file.status === status);
			}

			// @SERVICE 清理已完成的文件（可选）
			cleanupCompletedFiles(olderThanDays = 7) {
				const cutoffDate = new Date();
				cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

				let cleanedCount = 0;
				Object.keys(this.registry).forEach(fileId => {
					const file = this.registry[fileId];
					if (file.status === FILE_STATUS.COMPLETED &&
						new Date(file.updatedAt) < cutoffDate) {
						delete this.registry[fileId];
						cleanedCount++;
					}
				});

				if (cleanedCount > 0) {
					this.saveRegistry();
					console.log(`🧹 清理了 ${cleanedCount} 个过期的已完成文件记录`);
				}

				return cleanedCount;
			}

			// @SERVICE 重置文件状态（用于重新分析）
			resetFileStatus(fileId) {
				if (!this.registry[fileId]) {
					return false;
				}

				const file = this.registry[fileId];
				file.status = FILE_STATUS.PENDING;
				file.updatedAt = new Date().toISOString();
				file.error = null;
				file.result = null;

				this.saveRegistry();
				console.log(`🔄 文件状态已重置: ${file.name}`);
				return true;
			}

			// @SERVICE 获取统计信息
			getStatistics() {
				const files = Object.values(this.registry);
				const stats = {
					total: files.length,
					pending: 0,
					processing: 0,
					completed: 0,
					failed: 0,
					skipped: 0
				};

				files.forEach(file => {
					stats[file.status] = (stats[file.status] || 0) + 1;
				});

				return stats;
			}
		}

		// ==================== 分析进度管理器 ====================
		/**
		 * @CLASS 分析进度管理器
		 * 负责分析进度的实时保存和恢复
		 */
		class AnalysisProgressManager {
			constructor() {
				this.storage = new StorageManager();
				this.progress = this.loadProgress();
			}

			// @SERVICE 加载分析进度
			loadProgress() {
				return this.storage.load(STORAGE_KEYS.ANALYSIS_PROGRESS, {
					sessionId: null,
					startTime: null,
					totalFiles: 0,
					processedFiles: 0,
					completedFiles: 0,
					failedFiles: 0,
					currentBatch: [],
					isRunning: false,
					lastSaveTime: null
				});
			}

			// @SERVICE 保存分析进度
			saveProgress() {
				this.progress.lastSaveTime = new Date().toISOString();
				return this.storage.save(STORAGE_KEYS.ANALYSIS_PROGRESS, this.progress);
			}

			// @SERVICE 开始新的分析会话
			startSession(totalFiles) {
				this.progress = {
					sessionId: this.generateSessionId(),
					startTime: new Date().toISOString(),
					totalFiles: totalFiles,
					processedFiles: 0,
					completedFiles: 0,
					failedFiles: 0,
					currentBatch: [],
					isRunning: true,
					lastSaveTime: new Date().toISOString()
				};

				this.saveProgress();
				console.log(`🚀 分析会话开始: ${this.progress.sessionId}, 总文件数: ${totalFiles}`);
				return this.progress.sessionId;
			}

			// @SERVICE 更新进度
			updateProgress(data) {
				Object.assign(this.progress, data);
				this.saveProgress();
			}

			// @SERVICE 完成会话
			completeSession() {
				this.progress.isRunning = false;
				this.progress.endTime = new Date().toISOString();
				this.saveProgress();
				console.log(`✅ 分析会话完成: ${this.progress.sessionId}`);
			}

			// @SERVICE 检查是否有未完成的会话
			hasUnfinishedSession() {
				return this.progress.isRunning && this.progress.sessionId;
			}

			// @SERVICE 恢复会话
			resumeSession() {
				if (!this.hasUnfinishedSession()) {
					return null;
				}

				console.log(`🔄 恢复分析会话: ${this.progress.sessionId}`);
				return this.progress;
			}

			// @SERVICE 获取进度百分比
			getProgressPercentage() {
				if (this.progress.totalFiles === 0) return 0;
				return Math.round((this.progress.processedFiles / this.progress.totalFiles) * 100);
			}

			// @SERVICE 获取进度信息
			getProgressInfo() {
				return {
					...this.progress,
					percentage: this.getProgressPercentage(),
					remainingFiles: this.progress.totalFiles - this.progress.processedFiles,
					elapsedTime: this.progress.startTime ?
						Date.now() - new Date(this.progress.startTime).getTime() : 0
				};
			}

			// @UTIL 生成会话ID
			generateSessionId() {
				return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
			}

			// @SERVICE 清理旧会话
			cleanupOldSessions() {
				// 如果会话超过24小时且未运行，则清理
				if (this.progress.startTime) {
					const sessionAge = Date.now() - new Date(this.progress.startTime).getTime();
					const maxAge = 24 * 60 * 60 * 1000; // 24小时

					if (sessionAge > maxAge && !this.progress.isRunning) {
						this.progress = {
							sessionId: null,
							startTime: null,
							totalFiles: 0,
							processedFiles: 0,
							completedFiles: 0,
							failedFiles: 0,
							currentBatch: [],
							isRunning: false,
							lastSaveTime: null
						};
						this.saveProgress();
						console.log('🧹 清理了过期的分析会话');
						return true;
					}
				}
				return false;
			}
		}

		// @CONSTANTS 优先级级别定义
		const PRIORITY_LEVELS = {
			1: "一般咨询",
			2: "日常问题",
			3: "重要事项",
			4: "紧急处理",
			5: "特急情况"
		};

		// ==================== API配置管理器 ====================
		/**
		 * @CLASS API配置管理器
		 * 负责管理Kimi和Gemini API的配置和负载均衡
		 */
		class APIConfigManager {
			constructor() {
				this.storage = new StorageManager();
				this.config = this.loadConfig();
				this.loadExternalConfig();
			}

			// @SERVICE 加载API配置
			loadConfig() {
				return this.storage.load(STORAGE_KEYS.API_CONFIG, {
					kimi: {
						apiKey: 'sk-JcWzWW8FEnbrEvmvIY6N4vbcFzPxqXJBiFSbqej3Ff2uJBsY',
						baseURL: 'https://api.moonshot.cn/v1',
						model: 'kimi-k2-turbo-preview',
						maxConcurrency: 10,
						timeout: 30000,
						retryAttempts: 3,
						enabled: true,
						priority: 1
					},
					gemini: {
						apiKey: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
						baseURL: 'https://generativelanguage.googleapis.com/v1beta',
						model: 'gemini-2.5-pro',
						maxConcurrency: 10,
						timeout: 30000,
						retryAttempts: 3,
						enabled: true,
						priority: 2
					},
					loadBalancing: {
						strategy: 'round_robin', // round_robin, priority, random
						healthCheck: true,
						failoverEnabled: true
					}
				});
			}

			// @SERVICE 从外部文件加载配置
			async loadExternalConfig() {
				try {
					const response = await fetch('./config/api-keys.json');
					if (response.ok) {
						const externalConfig = await response.json();

						// 合并外部配置
						if (externalConfig.kimi) {
							Object.assign(this.config.kimi, externalConfig.kimi);
						}
						if (externalConfig.gemini) {
							Object.assign(this.config.gemini, externalConfig.gemini);
						}
						if (externalConfig.loadBalancing) {
							Object.assign(this.config.loadBalancing, externalConfig.loadBalancing);
						}

						this.saveConfig();
						console.log('✅ 外部API配置加载成功');
					}
				} catch (error) {
					console.warn('⚠️ 外部API配置加载失败，使用默认配置:', error);
				}
			}

			// @SERVICE 保存API配置
			saveConfig() {
				return this.storage.save(STORAGE_KEYS.API_CONFIG, this.config);
			}

			// @SERVICE 获取可用的引擎列表
			getAvailableEngines() {
				const engines = [];

				if (this.config.kimi.enabled && this.config.kimi.apiKey) {
					engines.push({
						name: LLM_ENGINES.KIMI,
						config: this.config.kimi,
						currentLoad: 0
					});
				}

				if (this.config.gemini.enabled && this.config.gemini.apiKey) {
					engines.push({
						name: LLM_ENGINES.GEMINI,
						config: this.config.gemini,
						currentLoad: 0
					});
				}

				// 按优先级排序
				engines.sort((a, b) => a.config.priority - b.config.priority);
				return engines;
			}

			// @SERVICE 选择最佳引擎
			selectEngine(strategy = null) {
				const engines = this.getAvailableEngines();
				if (engines.length === 0) {
					throw new Error('没有可用的AI引擎');
				}

				const selectedStrategy = strategy || this.config.loadBalancing.strategy;

				switch (selectedStrategy) {
					case 'priority':
						return engines[0]; // 返回优先级最高的

					case 'random':
						return engines[Math.floor(Math.random() * engines.length)];

					case 'round_robin':
					default:
						// 简单的轮询实现
						if (!this.lastSelectedIndex) {
							this.lastSelectedIndex = 0;
						} else {
							this.lastSelectedIndex = (this.lastSelectedIndex + 1) % engines.length;
						}
						return engines[this.lastSelectedIndex];
				}
			}

			// @SERVICE 获取引擎配置
			getEngineConfig(engineName) {
				return this.config[engineName] || null;
			}

			// @SERVICE 更新引擎状态
			updateEngineStatus(engineName, status) {
				if (this.config[engineName]) {
					this.config[engineName].lastStatus = status;
					this.config[engineName].lastUpdate = new Date().toISOString();
					this.saveConfig();
				}
			}

			// @SERVICE 健康检查
			async performHealthCheck() {
				const engines = this.getAvailableEngines();
				const results = {};

				for (const engine of engines) {
					try {
						const startTime = Date.now();
						// 发送简单的测试请求
						await this.testEngineConnection(engine.name);
						const responseTime = Date.now() - startTime;

						results[engine.name] = {
							healthy: true,
							responseTime: responseTime,
							lastCheck: new Date().toISOString()
						};
					} catch (error) {
						results[engine.name] = {
							healthy: false,
							error: error.message,
							lastCheck: new Date().toISOString()
						};
					}
				}

				return results;
			}

			// @SERVICE 测试引擎连接
			async testEngineConnection(engineName) {
				const config = this.getEngineConfig(engineName);
				if (!config) {
					throw new Error(`引擎配置不存在: ${engineName}`);
				}

				// 这里可以发送一个简单的测试请求
				// 暂时返回成功，实际实现时需要调用相应的API
				return Promise.resolve();
			}
		}

		// parseTxtContent 已迁移至 parser.js

		// parseTimestamp 已迁移至 parser.js

		// 进度详情元素
		const currentFileEl = document.getElementById('current-file');
		const completedCountEl = document.getElementById('completed-count');
		const totalCountEl = document.getElementById('total-count');

		// 控制按钮
		const pauseBtn = document.getElementById('pause-btn');
		const resumeBtn = document.getElementById('resume-btn');

		// ==================== 双LLM引擎API管理器 ====================
		/**
		 * @CLASS 双LLM引擎API管理器
		 * 负责Kimi和Gemini API的调用和负载均衡
		 */
		class DualLLMManager {
			constructor() {
				this.configManager = new APIConfigManager();
				this.activeRequests = new Map(); // 跟踪活跃请求
				this.requestQueue = []; // 请求队列
				this.maxConcurrentRequests = 10; // 总并发限制
			}

			// @SERVICE Kimi API调用
			async callKimiAPI(prompt, options = {}) {
				const config = this.configManager.getEngineConfig('kimi');
				if (!config || !config.enabled || !config.apiKey) {
					throw new Error('Kimi API未配置或未启用');
				}

				const requestConfig = {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${config.apiKey}`
					},
					body: JSON.stringify({
						model: config.model,
						messages: [{ role: 'user', content: prompt }],
						temperature: 0.3,
						...options
					})
				};

				return this.makeAPIRequest(
					`${config.baseURL}/chat/completions`,
					requestConfig,
					config,
					'kimi'
				);
			}

			// @SERVICE Gemini API调用
			async callGeminiAPI(prompt, options = {}) {
				const config = this.configManager.getEngineConfig('gemini');
				if (!config || !config.enabled || !config.apiKey) {
					throw new Error('Gemini API未配置或未启用');
				}

				const requestConfig = {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						contents: [{
							parts: [{ text: prompt }]
						}],
						generationConfig: {
							temperature: 0.3,
							maxOutputTokens: 2048,
							...options
						}
					})
				};

				const url = `${config.baseURL}/models/${config.model}:generateContent?key=${config.apiKey}`;
				return this.makeAPIRequest(url, requestConfig, config, 'gemini');
			}

			// @SERVICE 通用API请求方法
			async makeAPIRequest(url, requestConfig, engineConfig, engineName) {
				const requestId = this.generateRequestId();

				try {
					// 添加到活跃请求跟踪
					this.activeRequests.set(requestId, {
						engine: engineName,
						startTime: Date.now(),
						url: url
					});

					// 设置超时
					const controller = new AbortController();
					const timeoutId = setTimeout(() => controller.abort(), engineConfig.timeout);
					requestConfig.signal = controller.signal;

					// 发送请求
					const response = await fetch(url, requestConfig);
					clearTimeout(timeoutId);

					if (!response.ok) {
						throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
					}

					const data = await response.json();

					// 解析响应
					const result = this.parseAPIResponse(data, engineName);

					// 更新引擎状态
					this.configManager.updateEngineStatus(engineName, 'success');

					return result;

				} catch (error) {
					// 更新引擎状态
					this.configManager.updateEngineStatus(engineName, 'error');
					throw error;
				} finally {
					// 从活跃请求中移除
					this.activeRequests.delete(requestId);
				}
			}

			// @SERVICE 解析API响应
			parseAPIResponse(data, engineName) {
				try {
					if (engineName === 'kimi') {
						if (data.choices && data.choices[0] && data.choices[0].message) {
							const content = data.choices[0].message.content;
							return this.parseJSONResponse(content);
						}
					} else if (engineName === 'gemini') {
						if (data.candidates && data.candidates[0] && data.candidates[0].content) {
							const content = data.candidates[0].content.parts[0].text;
							return this.parseJSONResponse(content);
						}
					}

					throw new Error('无效的API响应格式');
				} catch (error) {
					console.error(`${engineName} API响应解析失败:`, error);
					throw error;
				}
			}

			// @SERVICE 解析JSON响应
			parseJSONResponse(content) {
				try {
					// 尝试提取JSON内容
					const jsonMatch = content.match(/\{[\s\S]*\}/);
					if (jsonMatch) {
						return JSON.parse(jsonMatch[0]);
					}

					// 如果没有找到JSON，尝试直接解析
					return JSON.parse(content);
				} catch (error) {
					console.error('JSON解析失败:', error);
					throw new Error('AI返回的内容不是有效的JSON格式');
				}
			}

			// @SERVICE 智能引擎选择和调用
			async analyzeConversation(conversationText, fileId = null) {
				const availableEngines = this.configManager.getAvailableEngines();
				if (availableEngines.length === 0) {
					throw new Error('没有可用的AI引擎');
				}

				// 选择引擎
				const selectedEngine = this.configManager.selectEngine();
				console.log(`🤖 选择引擎: ${selectedEngine.name} (文件ID: ${fileId})`);

				// 构建提示词
				const prompt = this.buildAnalysisPrompt(conversationText);

				// 调用相应的API
				try {
					let result;
					if (selectedEngine.name === LLM_ENGINES.KIMI) {
						result = await this.callKimiAPI(prompt);
					} else if (selectedEngine.name === LLM_ENGINES.GEMINI) {
						result = await this.callGeminiAPI(prompt);
					} else {
						throw new Error(`不支持的引擎类型: ${selectedEngine.name}`);
					}

					// 添加引擎信息到结果
					if (Array.isArray(result)) {
						result.forEach(item => {
							item._engine = selectedEngine.name;
							item._fileId = fileId;
						});
					} else {
						result._engine = selectedEngine.name;
						result._fileId = fileId;
					}

					return result;
				} catch (error) {
					console.error(`${selectedEngine.name} 引擎调用失败:`, error);

					// 如果启用了故障转移，尝试其他引擎
					if (this.configManager.config.loadBalancing.failoverEnabled && availableEngines.length > 1) {
						console.log('🔄 尝试故障转移到其他引擎...');
						return this.failoverAnalysis(conversationText, selectedEngine.name, fileId);
					}

					throw error;
				}
			}

			// @SERVICE 故障转移分析
			async failoverAnalysis(conversationText, failedEngine, fileId) {
				const availableEngines = this.configManager.getAvailableEngines()
					.filter(engine => engine.name !== failedEngine);

				if (availableEngines.length === 0) {
					throw new Error('所有AI引擎都不可用');
				}

				const fallbackEngine = availableEngines[0];
				console.log(`🔄 故障转移到: ${fallbackEngine.name}`);

				const prompt = this.buildAnalysisPrompt(conversationText);

				if (fallbackEngine.name === LLM_ENGINES.KIMI) {
					return await this.callKimiAPI(prompt);
				} else if (fallbackEngine.name === LLM_ENGINES.GEMINI) {
					return await this.callGeminiAPI(prompt);
				}
			}

			// @SERVICE 构建分析提示词
			buildAnalysisPrompt(conversationText) {
				return `
你是一名联系中心对话质量分析专家。请分析以下"客服-司机"对话，智能识别其中包含的独立问题数量，进行深度评估，并提取可复用的问答题集。

【核心任务】
1. 仔细阅读完整对话，识别其中包含的独立问题（一个问题 = 司机的一个具体诉求或关切点）
2. 如果对话只包含一个问题，返回单个JSON对象
3. 如果对话包含多个独立问题，返回JSON数组，每个元素对应一个问题的分析
4. 从每个问题中提取标准化的问答题集数据，构建司机知识库

【问题识别标准】
- 独立问题：司机提出的不同类型的诉求、咨询或投诉
- 相关问题：围绕同一核心问题的补充说明或追问，应合并为一个问题
- 时间跨度：考虑对话的时间跨度，长时间间隔后的新话题通常是新问题

【输入对话】
${conversationText}

【分析要求】
- 深度分析每个问题的背景、客服响应质量、解决效果、司机满意度
- 如果同一问题有多个客服参与，请综合评估团队协作效果
- 重点关注问题的实际解决程度，而非仅仅是回复的及时性
- 提取可操作的实用知识点，帮助其他司机解决类似问题

【问答题集提取要求】
- 识别可复用的通用问题（去除个人信息，如具体订单号、姓名等）
- 将客服回答标准化为清晰的操作步骤
- 只提取有明确解决方案的问答，过滤寒暄和无实质内容的对话
- 确保答案的准确性和可操作性
- 自动判断问题的通用性和重要性

【输出格式】
如果只有一个问题，返回单个JSON对象：
{
  "question": "问题详细描述",
  "questionBrief": "问题简述",
  "questionTags": ["标签1", "标签2"],
  "supportAgent": "主要客服姓名",
  "responseTime": 85,
  "resolutionRate": 90,
  "effectiveness": 88,
  "efficiency": 85,
  "satisfaction": 75,
  "attitude": 92,
  "knowledge": "实用知识点1；知识点2",
  "complexity": 3,
  "priority": 3,
  "extractedQA": {
    "question": "通用问题描述（去除个人信息）",
    "answer": "详细的标准答案",
    "operationSteps": ["步骤1", "步骤2", "步骤3"],
    "applicableScenarios": "适用场景描述",
    "difficulty": 2,
    "isCommon": true
  }
}

如果有多个问题，返回JSON数组：
[
  { /* 第一个问题的完整分析结果，包含extractedQA */ },
  { /* 第二个问题的完整分析结果，包含extractedQA */ },
  ...
]

【重要提醒】
- 必须基于对话内容客观分析，避免主观臆断
- 评分要充分利用0-100的范围，避免集中在中等水平
- 知识点要具体实用，能真正帮助其他司机
- 严格按照JSON格式输出，不要任何额外文字

【问答提取质量控制】
- 只提取有明确解决方案的问答，过滤纯粹的寒暄或无实质内容的对话
- 确保答案的准确性和可操作性，避免模糊或不完整的回答
- 将个人化信息（如订单号、姓名、具体时间）替换为通用描述
- 如果对话没有实质性的问答内容，extractedQA可以为null
`;
			}

			// @UTIL 生成请求ID
			generateRequestId() {
				return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
			}

			// @SERVICE 获取系统状态
			getSystemStatus() {
				return {
					activeRequests: this.activeRequests.size,
					maxConcurrentRequests: this.maxConcurrentRequests,
					availableEngines: this.configManager.getAvailableEngines().length,
					queueLength: this.requestQueue.length
				};
			}
		}

		// 注意：已移除本地分组逻辑，现在由AI智能判断问题数量

		// parseConversationFile 已迁移至 parser.js

		// 全局变量
		let selectedFiles = [];
		let processingState = {
			isRunning: false,
			isPaused: false,
			currentIndex: 0,
			results: [],
			totalStats: {
				processedFiles: 0,
				totalMessages: 0,
				totalQuestions: 0,
				satisfactionSum: 0,
				satisfactionCount: 0
			}
		};

		// @GLOBAL 新增：高并发处理管理器
		let fileRegistry = null;
		let progressManager = null;
		let apiConfigManager = null;
		let dualLLMManager = null;
		let queueManager = null;
		let appDataManager = null;
		let globalCharts = null;
		let reportGenerator = null;

		// @UTIL 显示调试提示
		const showDebugTips = () => {
			console.log(`💡 =================================`);
			console.log(`🔧 调试提示:`);
			console.log(`📊 查看处理统计: console.log(processingState)`);
			console.log(`📋 查看应用数据: console.log(appDataManager.data)`);
			console.log(`⚙️ 查看配置信息: console.log(CONFIG)`);
			console.log(`🔍 搜索日志: 使用浏览器控制台的过滤功能`);
			console.log(`   - 单问题分析: 过滤 "[单问题分析]"`);
			console.log(`   - 多问题分析: 过滤 "[多问题分析]"`);
			console.log(`   - 并发处理: 过滤 "[并发处理]"`);
			console.log(`   - 主处理: 过滤 "[主处理]"`);
			console.log(`   - API响应: 过滤 "📄" 或 "响应内容"`);
			console.log(`   - 错误信息: 过滤 "❌" 或 "错误"`);
			console.log(`   - JSON解析: 过滤 "JSON" 或 "解析"`);
			console.log(`💡 =================================`);
		};

		// 日志函数
		const log = (msg) => {
			const p = document.createElement("p");
			p.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
			logBox.appendChild(p);
			logBox.scrollTop = logBox.scrollHeight;
		};

		// ==================== 高并发队列管理器 ====================
		/**
		 * @CLASS 高并发队列管理器
		 * 负责管理文件处理队列和并发控制
		 */
		class ConcurrentQueueManager {
			constructor(maxConcurrency = 10) {
				this.maxConcurrency = maxConcurrency;
				this.activeJobs = new Map();
				this.pendingQueue = [];
				this.completedJobs = [];
				this.failedJobs = [];
				this.isProcessing = false;
				this.fileRegistry = new FileRegistry();
				this.progressManager = new AnalysisProgressManager();
				this.llmManager = new DualLLMManager();
			}

			// @SERVICE 添加文件到处理队列
			async addFiles(files) {
				const jobs = [];

				for (const file of files) {
					// 注册文件并检查是否重复
					const registration = this.fileRegistry.registerFile(file);

					if (registration.isDuplicate) {
						console.log(`⏭️ 跳过重复文件: ${file.name}`);
						continue;
					}

					// 创建处理任务
					const job = {
						id: registration.fileId,
						file: file,
						status: FILE_STATUS.PENDING,
						priority: 1,
						attempts: 0,
						maxAttempts: 3,
						createdAt: new Date().toISOString(),
						startedAt: null,
						completedAt: null,
						result: null,
						error: null
					};

					jobs.push(job);
					this.pendingQueue.push(job);
				}

				console.log(`📋 添加了 ${jobs.length} 个文件到处理队列`);
				return jobs;
			}

			// @SERVICE 开始处理队列
			async startProcessing() {
				if (this.isProcessing) {
					console.log('⚠️ 队列已在处理中');
					return;
				}

				this.isProcessing = true;
				const totalFiles = this.pendingQueue.length;

				if (totalFiles === 0) {
					console.log('📭 队列为空，无需处理');
					this.isProcessing = false;
					return;
				}

				// 开始分析会话
				const sessionId = this.progressManager.startSession(totalFiles);
				console.log(`🚀 开始处理队列，总文件数: ${totalFiles}, 最大并发: ${this.maxConcurrency}`);

				// 启动并发处理
				const promises = [];
				for (let i = 0; i < Math.min(this.maxConcurrency, totalFiles); i++) {
					promises.push(this.processNextJob());
				}

				try {
					await Promise.all(promises);
					console.log('✅ 队列处理完成');
				} catch (error) {
					console.error('❌ 队列处理出错:', error);
				} finally {
					this.isProcessing = false;
					this.progressManager.completeSession();
				}
			}

			// @SERVICE 处理下一个任务
			async processNextJob() {
				while (this.pendingQueue.length > 0 && this.isProcessing) {
					const job = this.pendingQueue.shift();
					if (!job) continue;

					try {
						await this.processJob(job);
					} catch (error) {
						console.error(`任务处理失败: ${job.file.name}`, error);
						await this.handleJobFailure(job, error);
					}
				}
			}

			// @SERVICE 处理单个任务
			async processJob(job) {
				const startTime = Date.now();
				job.status = FILE_STATUS.PROCESSING;
				job.startedAt = new Date().toISOString();
				job.attempts++;

				// 添加到活跃任务
				this.activeJobs.set(job.id, job);

				// 更新文件注册表状态
				this.fileRegistry.updateFileStatus(job.id, FILE_STATUS.PROCESSING, {
					attempts: job.attempts,
					startedAt: job.startedAt
				});

				// 更新进度
				this.progressManager.updateProgress({
					processedFiles: this.progressManager.progress.processedFiles + 1
				});

				console.log(`🔄 开始处理: ${job.file.name} (尝试 ${job.attempts}/${job.maxAttempts})`);

				try {
					// 解析文件内容
					const parseResult = await parseConversationFile(job.file);
					const { conversations } = parseResult;

					if (conversations.length === 0) {
						throw new Error('未找到有效对话内容');
					}

					// 构建对话文本
					const conversationText = conversations.map(conv =>
						`${conv.role}: ${conv.msg}`
					).join('\n');

					// 使用双LLM引擎分析
					const analysisResult = await this.llmManager.analyzeConversation(
						conversationText,
						job.id
					);

					// 处理分析结果
					const processedResult = await this.processAnalysisResult(
						analysisResult,
						conversations,
						job.file.name
					);

					// 任务完成
					job.status = FILE_STATUS.COMPLETED;
					job.completedAt = new Date().toISOString();
					job.result = processedResult;
					job.processingTime = Date.now() - startTime;

					// 更新文件注册表
					this.fileRegistry.updateFileStatus(job.id, FILE_STATUS.COMPLETED, {
						completedAt: job.completedAt,
						processingTime: job.processingTime,
						result: processedResult
					});

					// 移动到完成队列
					this.completedJobs.push(job);
					this.activeJobs.delete(job.id);

					// 更新进度
					this.progressManager.updateProgress({
						completedFiles: this.progressManager.progress.completedFiles + 1
					});

					console.log(`✅ 完成处理: ${job.file.name} (耗时: ${job.processingTime}ms)`);

				} catch (error) {
					await this.handleJobFailure(job, error);
				}
			}

			// @SERVICE 处理任务失败
			async handleJobFailure(job, error) {
				job.error = error.message;

				// 检查是否需要重试
				if (job.attempts < job.maxAttempts) {
					console.log(`🔄 任务失败，准备重试: ${job.file.name} (${job.attempts}/${job.maxAttempts})`);

					// 重新加入队列
					job.status = FILE_STATUS.PENDING;
					this.pendingQueue.push(job);

					// 更新文件注册表
					this.fileRegistry.updateFileStatus(job.id, FILE_STATUS.PENDING, {
						error: error.message,
						attempts: job.attempts
					});
				} else {
					console.error(`❌ 任务最终失败: ${job.file.name}`, error);

					// 标记为失败
					job.status = FILE_STATUS.FAILED;
					job.completedAt = new Date().toISOString();

					// 更新文件注册表
					this.fileRegistry.updateFileStatus(job.id, FILE_STATUS.FAILED, {
						error: error.message,
						completedAt: job.completedAt,
						attempts: job.attempts
					});

					// 移动到失败队列
					this.failedJobs.push(job);

					// 更新进度
					this.progressManager.updateProgress({
						failedFiles: this.progressManager.progress.failedFiles + 1
					});
				}

				// 从活跃任务中移除
				this.activeJobs.delete(job.id);
			}

			// @SERVICE 处理分析结果
			async processAnalysisResult(analysisResult, conversations, fileName) {
				// 这里可以添加结果后处理逻辑
				// 例如数据验证、格式化等

				// 确保结果是数组格式
				const results = Array.isArray(analysisResult) ? analysisResult : [analysisResult];

				// 为每个结果添加元数据
				results.forEach(result => {
					result._fileName = fileName;
					result._processedAt = new Date().toISOString();
					result._conversationCount = conversations.length;
				});

				return results;
			}

			// @SERVICE 暂停处理
			pauseProcessing() {
				this.isProcessing = false;
				console.log('⏸️ 队列处理已暂停');
			}

			// @SERVICE 恢复处理
			async resumeProcessing() {
				if (this.pendingQueue.length > 0) {
					console.log('▶️ 恢复队列处理');
					await this.startProcessing();
				}
			}

			// @SERVICE 获取队列状态
			getQueueStatus() {
				return {
					isProcessing: this.isProcessing,
					pending: this.pendingQueue.length,
					active: this.activeJobs.size,
					completed: this.completedJobs.length,
					failed: this.failedJobs.length,
					maxConcurrency: this.maxConcurrency,
					progress: this.progressManager.getProgressInfo()
				};
			}

			// @SERVICE 清理队列
			clearQueue() {
				this.pendingQueue = [];
				this.activeJobs.clear();
				this.completedJobs = [];
				this.failedJobs = [];
				console.log('🧹 队列已清理');
			}

			// @SERVICE 动态调整并发数
			adjustConcurrency(newConcurrency) {
				const oldConcurrency = this.maxConcurrency;
				this.maxConcurrency = Math.max(1, Math.min(20, newConcurrency));
				console.log(`⚙️ 并发数调整: ${oldConcurrency} -> ${this.maxConcurrency}`);
			}
		}

		/**
		 * @MANAGER 详细分析报告生成器
		 * 负责生成综合性的分析报告，包含执行摘要、数据统计、问题分类等
		 */
		class ReportGenerator {
			constructor() {
				this.storage = new StorageManager();
				this.llmManager = null; // 将在初始化时设置
				this.isGenerating = false;
				this.currentProgress = 0;
				this.currentStep = '';
				this.reportHistory = this.loadReportHistory();
				this.reportTemplates = this.loadReportTemplates();
			}

			// @SERVICE 初始化LLM管理器
			initializeLLMManager(llmManager) {
				this.llmManager = llmManager;
			}

			// @SERVICE 加载报告历史记录
			loadReportHistory() {
				return this.storage.load(STORAGE_KEYS.REPORT_HISTORY, []);
			}

			// @SERVICE 保存报告历史记录
			saveReportHistory() {
				return this.storage.save(STORAGE_KEYS.REPORT_HISTORY, this.reportHistory);
			}

			// @SERVICE 加载报告模板
			loadReportTemplates() {
				const defaultTemplates = {
					comprehensive: {
						name: '综合报告',
						sections: ['summary', 'statistics', 'categories', 'service', 'knowledge', 'details'],
						description: '包含所有分析维度的完整报告'
					},
					summary: {
						name: '摘要报告',
						sections: ['summary', 'statistics', 'categories'],
						description: '重点关注关键指标和趋势的简化报告'
					},
					detailed: {
						name: '详细报告',
						sections: ['summary', 'statistics', 'categories', 'service', 'knowledge', 'details', 'recommendations'],
						description: '包含详细分析和改进建议的深度报告'
					}
				};
				return this.storage.load(STORAGE_KEYS.REPORT_TEMPLATES, defaultTemplates);
			}

			// @SERVICE 保存报告模板
			saveReportTemplates() {
				return this.storage.save(STORAGE_KEYS.REPORT_TEMPLATES, this.reportTemplates);
			}

			// @SERVICE 生成报告
			async generateReport(config) {
				if (this.isGenerating) {
					throw new Error('报告生成正在进行中，请等待完成');
				}

				this.isGenerating = true;
				this.currentProgress = 0;
				this.currentStep = '初始化报告生成...';

				try {
					// 1. 验证配置
					const validatedConfig = this.validateConfig(config);
					this.updateProgress(10, '配置验证完成');

					// 2. 收集和筛选数据
					const analysisData = await this.collectAnalysisData(validatedConfig);
					this.updateProgress(30, '数据收集完成');

					// 3. 使用LLM进行深度分析
					const deepAnalysis = await this.performDeepAnalysis(analysisData, validatedConfig);
					this.updateProgress(60, '深度分析完成');

					// 4. 生成报告内容
					const reportContent = await this.generateReportContent(analysisData, deepAnalysis, validatedConfig);
					this.updateProgress(80, '报告内容生成完成');

					// 5. 保存到历史记录
					const reportRecord = this.saveToHistory(reportContent, validatedConfig);
					this.updateProgress(100, '报告生成完成');

					return {
						success: true,
						reportId: reportRecord.id,
						content: reportContent,
						config: validatedConfig
					};

				} catch (error) {
					console.error('报告生成失败:', error);
					throw error;
				} finally {
					this.isGenerating = false;
				}
			}

			// @SERVICE 验证报告配置
			validateConfig(config) {
				const validated = {
					selectedFiles: config.selectedFiles || [],
					startDate: config.startDate || null,
					endDate: config.endDate || null,
					reportType: config.reportType || 'comprehensive',
					exportFormats: config.exportFormats || ['html'],
					template: config.template || 'comprehensive'
				};

				// 验证文件选择
				if (validated.selectedFiles.length === 0) {
					throw new Error('请至少选择一个文件');
				}

				// 验证日期范围
				if (validated.startDate && validated.endDate) {
					const start = new Date(validated.startDate);
					const end = new Date(validated.endDate);
					if (start > end) {
						throw new Error('开始日期不能晚于结束日期');
					}
				}

				return validated;
			}

			// @SERVICE 收集分析数据
			async collectAnalysisData(config) {
				this.updateProgress(15, '正在收集分析数据...');

				if (!appDataManager) {
					throw new Error('数据管理器未初始化');
				}

				const data = appDataManager.data;

				// 根据配置筛选数据
				const filteredData = {
					drivers: data.drivers || {},
					supportAgents: data.supportAgents || {},
					knowledge: data.knowledge || [],
					questionCategories: data.questionCategories || {},
					questionTags: data.questionTags || {},
					metrics: data.metrics || [],
					detailedQuestions: data.detailedQuestions || [],
					qaDataset: data.qaDataset || []
				};

				// 应用时间范围筛选
				if (config.startDate || config.endDate) {
					filteredData.metrics = this.filterByDateRange(filteredData.metrics, config.startDate, config.endDate);
					filteredData.detailedQuestions = this.filterByDateRange(filteredData.detailedQuestions, config.startDate, config.endDate);
				}

				return filteredData;
			}

			// @SERVICE 按日期范围筛选数据
			filterByDateRange(dataArray, startDate, endDate) {
				if (!startDate && !endDate) return dataArray;

				const start = startDate ? new Date(startDate) : null;
				const end = endDate ? new Date(endDate) : null;

				return dataArray.filter(item => {
					const itemDate = new Date(item.timestamp || item.createdAt || Date.now());

					if (start && itemDate < start) return false;
					if (end && itemDate > end) return false;

					return true;
				});
			}

			// @SERVICE 更新进度
			updateProgress(percentage, step) {
				this.currentProgress = percentage;
				this.currentStep = step;

				// 触发UI更新事件
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new CustomEvent('reportProgressUpdate', {
						detail: { progress: percentage, step: step }
					}));
				}
			}

			// @SERVICE 使用LLM进行深度分析
			async performDeepAnalysis(analysisData, config) {
				this.updateProgress(35, '正在进行深度分析...');

				if (!this.llmManager) {
					throw new Error('LLM管理器未初始化');
				}

				const analysisPrompts = this.buildAnalysisPrompts(analysisData, config);
				const analysisResults = {};

				// 并发执行多个分析任务
				const analysisPromises = [];

				// 执行摘要分析
				if (config.template === 'comprehensive' || config.template === 'detailed') {
					analysisPromises.push(
						this.llmManager.callAPI(analysisPrompts.summary, 'kimi')
							.then(result => { analysisResults.summary = result; })
							.catch(error => {
								console.warn('执行摘要分析失败:', error);
								analysisResults.summary = this.generateFallbackSummary(analysisData);
							})
					);
				}

				// 问题分类深度分析
				analysisPromises.push(
					this.llmManager.callAPI(analysisPrompts.categories, 'gemini')
						.then(result => { analysisResults.categories = result; })
						.catch(error => {
							console.warn('问题分类分析失败:', error);
							analysisResults.categories = this.generateFallbackCategories(analysisData);
						})
				);

				// 等待所有分析完成
				await Promise.all(analysisPromises);
				this.updateProgress(55, '深度分析处理中...');

				return analysisResults;
			}

			// @SERVICE 构建分析提示词
			buildAnalysisPrompts(analysisData, config) {
				const dataStats = this.calculateDataStatistics(analysisData);

				return {
					summary: `
你是一名专业的客服数据分析师。请基于以下司机客服对话分析数据，生成一份执行摘要。

【数据概况】
- 分析文件数: ${config.selectedFiles.length}
- 对话总数: ${dataStats.totalConversations}
- 问题总数: ${dataStats.totalQuestions}
- 时间范围: ${config.startDate || '全部'} 至 ${config.endDate || '全部'}

【关键数据】
- 平均满意度: ${dataStats.avgSatisfaction.toFixed(1)}%
- 平均解决率: ${dataStats.avgResolutionRate.toFixed(1)}%
- 平均响应时间: ${dataStats.avgResponseTime.toFixed(1)}分钟
- 主要问题类型: ${dataStats.topCategories.join(', ')}

请生成一份简洁的执行摘要，包含：
1. 整体服务质量评估
2. 主要发现和趋势
3. 关键改进建议

要求：
- 语言简洁专业
- 突出关键数据
- 提供可操作的建议
- 字数控制在300字以内

请直接返回摘要内容，不需要额外格式。`,

					categories: `
你是一名客服质量分析专家。请基于以下问题分类数据，进行深度分析。

【问题分类统计】
${Object.entries(analysisData.questionCategories).map(([category, count]) =>
	`- ${category}: ${count}次`).join('\n')}

【问题标签分布】
${Object.entries(analysisData.questionTags).map(([tag, count]) =>
	`- ${tag}: ${count}次`).join('\n')}

请分析：
1. 问题类型分布特点
2. 高频问题的根本原因
3. 问题复杂度趋势
4. 改进优先级建议

要求：
- 深入分析问题背后的原因
- 识别系统性问题
- 提供具体的改进措施
- 字数控制在400字以内

请直接返回分析内容。`
				};
			}

			// @SERVICE 计算数据统计
			calculateDataStatistics(analysisData) {
				const metrics = analysisData.metrics || [];
				const categories = analysisData.questionCategories || {};

				// 计算平均值
				const avgSatisfaction = metrics.length > 0 ?
					metrics.reduce((sum, m) => sum + (m.satisfaction || 0), 0) / metrics.length : 0;
				const avgResolutionRate = metrics.length > 0 ?
					metrics.reduce((sum, m) => sum + (m.resolutionRate || 0), 0) / metrics.length : 0;
				const avgResponseTime = metrics.length > 0 ?
					metrics.reduce((sum, m) => sum + (m.responseTime || 0), 0) / metrics.length : 0;

				// 获取前5个问题类型
				const topCategories = Object.entries(categories)
					.sort(([,a], [,b]) => b - a)
					.slice(0, 5)
					.map(([category]) => category);

				return {
					totalConversations: metrics.length,
					totalQuestions: Object.values(categories).reduce((sum, count) => sum + count, 0),
					avgSatisfaction,
					avgResolutionRate,
					avgResponseTime,
					topCategories
				};
			}

			// @SERVICE 生成备用摘要（当LLM调用失败时）
			generateFallbackSummary(analysisData) {
				const stats = this.calculateDataStatistics(analysisData);
				return `
基于${stats.totalConversations}次对话的分析，系统识别出${stats.totalQuestions}个问题。
整体服务质量表现为：平均满意度${stats.avgSatisfaction.toFixed(1)}%，
平均解决率${stats.avgResolutionRate.toFixed(1)}%，
平均响应时间${stats.avgResponseTime.toFixed(1)}分钟。
主要问题集中在：${stats.topCategories.join('、')}等领域。
建议重点关注高频问题的解决方案优化和客服培训。
				`.trim();
			}

			// @SERVICE 生成备用分类分析
			generateFallbackCategories(analysisData) {
				const categories = Object.entries(analysisData.questionCategories)
					.sort(([,a], [,b]) => b - a)
					.slice(0, 5);

				return `
问题分类分析显示：${categories.map(([cat, count]) => `${cat}(${count}次)`).join('、')}
是当前的主要问题类型。建议针对高频问题制定标准化解决方案，
提升客服处理效率和用户满意度。
				`.trim();
			}

			// @SERVICE 生成报告内容
			async generateReportContent(analysisData, deepAnalysis, config) {
				this.updateProgress(65, '正在生成报告内容...');

				const template = this.reportTemplates[config.template];
				const reportContent = {
					title: this.generateReportTitle(config),
					generatedAt: new Date().toISOString(),
					config: config,
					sections: {}
				};

				// 根据模板生成各个部分
				for (const sectionName of template.sections) {
					switch (sectionName) {
						case 'summary':
							reportContent.sections.summary = this.generateSummarySection(analysisData, deepAnalysis);
							break;
						case 'statistics':
							reportContent.sections.statistics = this.generateStatisticsSection(analysisData);
							break;
						case 'categories':
							reportContent.sections.categories = this.generateCategoriesSection(analysisData, deepAnalysis);
							break;
						case 'service':
							reportContent.sections.service = this.generateServiceSection(analysisData, deepAnalysis);
							break;
						case 'knowledge':
							reportContent.sections.knowledge = this.generateKnowledgeSection(analysisData, deepAnalysis);
							break;
						case 'details':
							reportContent.sections.details = this.generateDetailsSection(analysisData);
							break;
						case 'recommendations':
							reportContent.sections.recommendations = this.generateRecommendationsSection(analysisData, deepAnalysis);
							break;
					}
					this.updateProgress(65 + (template.sections.indexOf(sectionName) + 1) * 10 / template.sections.length,
						`生成${sectionName}部分...`);
				}

				return reportContent;
			}

			// @SERVICE 生成报告标题
			generateReportTitle(config) {
				const typeNames = {
					comprehensive: '综合分析报告',
					summary: '摘要分析报告',
					detailed: '详细分析报告'
				};

				const dateStr = new Date().toLocaleDateString('zh-CN');
				return `司机客服对话${typeNames[config.reportType] || '分析报告'} - ${dateStr}`;
			}

			// @SERVICE 生成执行摘要部分
			generateSummarySection(analysisData, deepAnalysis) {
				const stats = this.calculateDataStatistics(analysisData);

				return {
					title: '执行摘要',
					content: deepAnalysis.summary || this.generateFallbackSummary(analysisData),
					keyMetrics: {
						totalConversations: stats.totalConversations,
						totalQuestions: stats.totalQuestions,
						avgSatisfaction: stats.avgSatisfaction,
						avgResolutionRate: stats.avgResolutionRate,
						avgResponseTime: stats.avgResponseTime
					}
				};
			}

			// @SERVICE 生成数据统计部分
			generateStatisticsSection(analysisData) {
				const stats = this.calculateDataStatistics(analysisData);
				const categories = Object.entries(analysisData.questionCategories)
					.sort(([,a], [,b]) => b - a);
				const tags = Object.entries(analysisData.questionTags)
					.sort(([,a], [,b]) => b - a);

				return {
					title: '数据统计',
					basicStats: {
						totalConversations: stats.totalConversations,
						totalQuestions: stats.totalQuestions,
						totalDrivers: Object.keys(analysisData.drivers).length,
						totalAgents: Object.keys(analysisData.supportAgents).length,
						totalKnowledge: analysisData.knowledge.length,
						totalQAItems: analysisData.qaDataset.length
					},
					qualityMetrics: {
						avgSatisfaction: stats.avgSatisfaction,
						avgResolutionRate: stats.avgResolutionRate,
						avgResponseTime: stats.avgResponseTime
					},
					categoryDistribution: categories.slice(0, 10),
					tagDistribution: tags.slice(0, 15)
				};
			}

			// @SERVICE 生成问题分类部分
			generateCategoriesSection(analysisData, deepAnalysis) {
				const categories = Object.entries(analysisData.questionCategories)
					.sort(([,a], [,b]) => b - a);

				return {
					title: '问题分类分析',
					analysis: deepAnalysis.categories || this.generateFallbackCategories(analysisData),
					categoryStats: categories,
					topCategories: categories.slice(0, 5),
					categoryTrends: this.analyzeCategoryTrends(analysisData)
				};
			}

			// @SERVICE 分析分类趋势
			analyzeCategoryTrends(analysisData) {
				// 这里可以添加更复杂的趋势分析逻辑
				// 目前返回基础统计
				const categories = Object.entries(analysisData.questionCategories)
					.sort(([,a], [,b]) => b - a);

				return {
					mostFrequent: categories[0] ? categories[0][0] : '无',
					leastFrequent: categories[categories.length - 1] ? categories[categories.length - 1][0] : '无',
					totalCategories: categories.length
				};
			}

			// @SERVICE 生成服务效果部分
			generateServiceSection(analysisData, deepAnalysis) {
				const agents = Object.entries(analysisData.supportAgents)
					.map(([name, data]) => ({
						name,
						totalQuestions: data.totalQuestions || 0,
						avgSatisfaction: data.avgSatisfaction || 0,
						avgEffectiveness: data.avgEffectiveness || 0
					}))
					.sort((a, b) => b.avgSatisfaction - a.avgSatisfaction);

				return {
					title: '服务效果评估',
					analysis: deepAnalysis.service || '服务效果分析数据不足',
					agentPerformance: agents.slice(0, 10),
					topPerformers: agents.slice(0, 3),
					improvementNeeded: agents.slice(-3).reverse()
				};
			}

			// @SERVICE 生成知识库部分
			generateKnowledgeSection(analysisData, deepAnalysis) {
				return {
					title: '知识库覆盖度分析',
					analysis: deepAnalysis.knowledge || '知识库分析数据不足',
					knowledgeStats: {
						totalItems: analysisData.knowledge.length,
						qaItems: analysisData.qaDataset.length,
						coverageRate: this.calculateKnowledgeCoverage(analysisData)
					},
					knowledgeGaps: this.identifyKnowledgeGaps(analysisData),
					recommendations: this.generateKnowledgeRecommendations(analysisData)
				};
			}

			// @SERVICE 计算知识库覆盖率
			calculateKnowledgeCoverage(analysisData) {
				const totalQuestions = Object.values(analysisData.questionCategories).reduce((sum, count) => sum + count, 0);
				const coveredQuestions = analysisData.qaDataset.length;
				return totalQuestions > 0 ? (coveredQuestions / totalQuestions * 100) : 0;
			}

			// @SERVICE 识别知识盲点
			identifyKnowledgeGaps(analysisData) {
				const categories = Object.entries(analysisData.questionCategories)
					.sort(([,a], [,b]) => b - a);

				// 简单的盲点识别：高频问题但缺少对应的问答条目
				const gaps = categories.filter(([category, count]) => {
					const hasQA = analysisData.qaDataset.some(qa =>
						qa.question.includes(category) || qa.tags?.includes(category)
					);
					return count > 5 && !hasQA; // 出现5次以上但没有对应问答
				});

				return gaps.slice(0, 5).map(([category, count]) => ({
					category,
					frequency: count,
					priority: count > 20 ? 'high' : count > 10 ? 'medium' : 'low'
				}));
			}

			// @SERVICE 生成知识库建议
			generateKnowledgeRecommendations(analysisData) {
				const gaps = this.identifyKnowledgeGaps(analysisData);
				const recommendations = [];

				if (gaps.length > 0) {
					recommendations.push({
						type: 'gap_filling',
						title: '填补知识盲点',
						description: `需要为以下高频问题类型创建标准答案：${gaps.map(g => g.category).join('、')}`
					});
				}

				if (analysisData.qaDataset.length < 50) {
					recommendations.push({
						type: 'expansion',
						title: '扩充问答库',
						description: '当前问答库条目较少，建议增加更多常见问题的标准答案'
					});
				}

				return recommendations;
			}

			// @SERVICE 生成详细记录部分
			generateDetailsSection(analysisData) {
				const recentQuestions = analysisData.detailedQuestions
					.sort((a, b) => new Date(b.timestamp || 0) - new Date(a.timestamp || 0))
					.slice(0, 20);

				return {
					title: '详细问答记录',
					recentQuestions: recentQuestions.map(q => ({
						question: q.question || '未知问题',
						questionBrief: q.questionBrief || '无简述',
						supportAgent: q.supportAgent || '未知客服',
						satisfaction: q.satisfaction || 0,
						resolutionRate: q.resolutionRate || 0,
						tags: q.questionTags || [],
						timestamp: q.timestamp || null
					})),
					totalRecords: analysisData.detailedQuestions.length
				};
			}

			// @SERVICE 生成改进建议部分
			generateRecommendationsSection(analysisData, deepAnalysis) {
				const recommendations = [];
				const stats = this.calculateDataStatistics(analysisData);

				// 基于数据生成建议
				if (stats.avgSatisfaction < 80) {
					recommendations.push({
						priority: 'high',
						category: '服务质量',
						title: '提升客户满意度',
						description: `当前平均满意度为${stats.avgSatisfaction.toFixed(1)}%，建议加强客服培训和服务流程优化。`,
						actions: ['制定客服培训计划', '优化服务流程', '建立质量监控机制']
					});
				}

				if (stats.avgResponseTime > 10) {
					recommendations.push({
						priority: 'medium',
						category: '响应效率',
						title: '缩短响应时间',
						description: `当前平均响应时间为${stats.avgResponseTime.toFixed(1)}分钟，建议优化响应流程。`,
						actions: ['建立快速响应机制', '优化工单分配', '增加客服人员']
					});
				}

				const knowledgeGaps = this.identifyKnowledgeGaps(analysisData);
				if (knowledgeGaps.length > 0) {
					recommendations.push({
						priority: 'medium',
						category: '知识库',
						title: '完善知识库',
						description: `发现${knowledgeGaps.length}个知识盲点，需要补充相关内容。`,
						actions: ['创建标准答案模板', '收集常见问题', '定期更新知识库']
					});
				}

				return {
					title: '改进建议',
					recommendations: recommendations,
					summary: `基于分析结果，系统识别出${recommendations.length}个主要改进方向，建议按优先级逐步实施。`
				};
			}

			// @SERVICE 保存到历史记录
			saveToHistory(reportContent, config) {
				const reportRecord = {
					id: Date.now().toString(),
					title: reportContent.title,
					type: config.reportType,
					template: config.template,
					generatedAt: reportContent.generatedAt,
					fileCount: config.selectedFiles.length,
					dateRange: {
						start: config.startDate,
						end: config.endDate
					},
					content: reportContent
				};

				this.reportHistory.unshift(reportRecord);

				// 只保留最近50个报告
				if (this.reportHistory.length > 50) {
					this.reportHistory = this.reportHistory.slice(0, 50);
				}

				this.saveReportHistory();
				return reportRecord;
			}

			// @SERVICE 获取报告历史
			getReportHistory() {
				return this.reportHistory;
			}

			// @SERVICE 删除历史报告
			deleteReport(reportId) {
				this.reportHistory = this.reportHistory.filter(report => report.id !== reportId);
				this.saveReportHistory();
				return true;
			}

			// @SERVICE 获取报告内容
			getReportContent(reportId) {
				const report = this.reportHistory.find(r => r.id === reportId);
				return report ? report.content : null;
			}

			// @SERVICE 取消报告生成
			cancelGeneration() {
				this.isGenerating = false;
				this.currentProgress = 0;
				this.currentStep = '已取消';
			}

			// @SERVICE 获取当前进度
			getProgress() {
				return {
					isGenerating: this.isGenerating,
					progress: this.currentProgress,
					step: this.currentStep
				};
			}
		}

		/**
		 * @MANAGER 报告导出器
		 * 负责将报告内容导出为不同格式（HTML、PDF、Word等）
		 */
		class ReportExporter {
			constructor() {
				this.supportedFormats = ['html', 'pdf', 'word'];
			}

			// @SERVICE 导出报告
			async exportReport(reportContent, formats = ['html']) {
				const results = {};

				for (const format of formats) {
					try {
						switch (format.toLowerCase()) {
							case 'html':
								results.html = await this.exportToHTML(reportContent);
								break;
							case 'pdf':
								results.pdf = await this.exportToPDF(reportContent);
								break;
							case 'word':
								results.word = await this.exportToWord(reportContent);
								break;
							default:
								console.warn(`不支持的导出格式: ${format}`);
						}
					} catch (error) {
						console.error(`导出${format}格式失败:`, error);
						results[format] = { error: error.message };
					}
				}

				return results;
			}

			// @SERVICE 导出为HTML
			async exportToHTML(reportContent) {
				const htmlContent = this.generateHTMLContent(reportContent);
				const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
				const url = URL.createObjectURL(blob);

				const filename = `${reportContent.title.replace(/[^\w\s-]/g, '')}_${new Date().toISOString().split('T')[0]}.html`;

				// 创建下载链接
				const link = document.createElement('a');
				link.href = url;
				link.download = filename;
				link.style.display = 'none';
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);

				// 清理URL对象
				setTimeout(() => URL.revokeObjectURL(url), 1000);

				return {
					success: true,
					filename: filename,
					size: blob.size
				};
			}

			// @SERVICE 生成HTML内容
			generateHTMLContent(reportContent) {
				const sections = reportContent.sections || {};

				return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>${reportContent.title}</title>
	<style>
		body {
			font-family: 'Microsoft YaHei', Arial, sans-serif;
			line-height: 1.6;
			color: #333;
			max-width: 1200px;
			margin: 0 auto;
			padding: 20px;
			background: #f5f5f5;
		}
		.report-container {
			background: white;
			padding: 40px;
			border-radius: 8px;
			box-shadow: 0 2px 10px rgba(0,0,0,0.1);
		}
		.report-header {
			text-align: center;
			border-bottom: 3px solid #007bff;
			padding-bottom: 20px;
			margin-bottom: 30px;
		}
		.report-title {
			font-size: 2.5rem;
			color: #007bff;
			margin: 0 0 10px 0;
		}
		.report-meta {
			color: #666;
			font-size: 1rem;
		}
		.section {
			margin-bottom: 40px;
		}
		.section-title {
			font-size: 1.8rem;
			color: #333;
			border-left: 4px solid #007bff;
			padding-left: 15px;
			margin-bottom: 20px;
		}
		.metrics-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
			gap: 20px;
			margin: 20px 0;
		}
		.metric-card {
			background: #f8f9fa;
			padding: 20px;
			border-radius: 6px;
			text-align: center;
			border: 1px solid #e9ecef;
		}
		.metric-value {
			font-size: 2rem;
			font-weight: bold;
			color: #007bff;
		}
		.metric-label {
			color: #666;
			margin-top: 5px;
		}
		.data-table {
			width: 100%;
			border-collapse: collapse;
			margin: 20px 0;
		}
		.data-table th,
		.data-table td {
			padding: 12px;
			text-align: left;
			border-bottom: 1px solid #ddd;
		}
		.data-table th {
			background-color: #f8f9fa;
			font-weight: bold;
		}
		.recommendation {
			background: #fff3cd;
			border: 1px solid #ffeaa7;
			border-radius: 6px;
			padding: 15px;
			margin: 10px 0;
		}
		.recommendation-title {
			font-weight: bold;
			color: #856404;
		}
		.priority-high { border-left: 4px solid #dc3545; }
		.priority-medium { border-left: 4px solid #ffc107; }
		.priority-low { border-left: 4px solid #28a745; }
		.footer {
			text-align: center;
			margin-top: 40px;
			padding-top: 20px;
			border-top: 1px solid #ddd;
			color: #666;
			font-size: 0.9rem;
		}
	</style>
</head>
<body>
	<div class="report-container">
		<div class="report-header">
			<h1 class="report-title">${reportContent.title}</h1>
			<div class="report-meta">
				生成时间: ${new Date(reportContent.generatedAt).toLocaleString('zh-CN')}
			</div>
		</div>

		${this.generateSectionHTML('summary', sections.summary)}
		${this.generateSectionHTML('statistics', sections.statistics)}
		${this.generateSectionHTML('categories', sections.categories)}
		${this.generateSectionHTML('service', sections.service)}
		${this.generateSectionHTML('knowledge', sections.knowledge)}
		${this.generateSectionHTML('details', sections.details)}
		${this.generateSectionHTML('recommendations', sections.recommendations)}

		<div class="footer">
			<p>本报告由司机客服对话分析系统自动生成</p>
		</div>
	</div>
</body>
</html>`;
			}

			// @SERVICE 生成单个部分的HTML
			generateSectionHTML(sectionType, sectionData) {
				if (!sectionData) return '';

				switch (sectionType) {
					case 'summary':
						return this.generateSummaryHTML(sectionData);
					case 'statistics':
						return this.generateStatisticsHTML(sectionData);
					case 'categories':
						return this.generateCategoriesHTML(sectionData);
					case 'service':
						return this.generateServiceHTML(sectionData);
					case 'knowledge':
						return this.generateKnowledgeHTML(sectionData);
					case 'details':
						return this.generateDetailsHTML(sectionData);
					case 'recommendations':
						return this.generateRecommendationsHTML(sectionData);
					default:
						return '';
				}
			}

			// @SERVICE 生成摘要HTML
			generateSummaryHTML(data) {
				return `
		<div class="section">
			<h2 class="section-title">${data.title}</h2>
			<div class="metrics-grid">
				<div class="metric-card">
					<div class="metric-value">${data.keyMetrics.totalConversations}</div>
					<div class="metric-label">对话总数</div>
				</div>
				<div class="metric-card">
					<div class="metric-value">${data.keyMetrics.totalQuestions}</div>
					<div class="metric-label">问题总数</div>
				</div>
				<div class="metric-card">
					<div class="metric-value">${data.keyMetrics.avgSatisfaction.toFixed(1)}%</div>
					<div class="metric-label">平均满意度</div>
				</div>
				<div class="metric-card">
					<div class="metric-value">${data.keyMetrics.avgResolutionRate.toFixed(1)}%</div>
					<div class="metric-label">平均解决率</div>
				</div>
			</div>
			<div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-top: 20px;">
				<p style="margin: 0; line-height: 1.8;">${data.content}</p>
			</div>
		</div>`;
			}

			// @SERVICE 生成统计HTML
			generateStatisticsHTML(data) {
				const categoryRows = data.categoryDistribution.map(([category, count]) =>
					`<tr><td>${category}</td><td>${count}</td></tr>`).join('');

				return `
		<div class="section">
			<h2 class="section-title">${data.title}</h2>
			<div class="metrics-grid">
				<div class="metric-card">
					<div class="metric-value">${data.basicStats.totalConversations}</div>
					<div class="metric-label">对话总数</div>
				</div>
				<div class="metric-card">
					<div class="metric-value">${data.basicStats.totalDrivers}</div>
					<div class="metric-label">司机数量</div>
				</div>
				<div class="metric-card">
					<div class="metric-value">${data.basicStats.totalAgents}</div>
					<div class="metric-label">客服数量</div>
				</div>
				<div class="metric-card">
					<div class="metric-value">${data.basicStats.totalKnowledge}</div>
					<div class="metric-label">知识条目</div>
				</div>
			</div>
			<h3>问题分类分布</h3>
			<table class="data-table">
				<thead>
					<tr><th>问题类型</th><th>出现次数</th></tr>
				</thead>
				<tbody>
					${categoryRows}
				</tbody>
			</table>
		</div>`;
			}

			// @SERVICE 生成分类HTML
			generateCategoriesHTML(data) {
				const categoryRows = data.categoryStats.slice(0, 10).map(([category, count]) =>
					`<tr><td>${category}</td><td>${count}</td></tr>`).join('');

				return `
		<div class="section">
			<h2 class="section-title">${data.title}</h2>
			<div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
				<p style="margin: 0; line-height: 1.8;">${data.analysis}</p>
			</div>
			<h3>问题分类统计</h3>
			<table class="data-table">
				<thead>
					<tr><th>问题类型</th><th>出现次数</th></tr>
				</thead>
				<tbody>
					${categoryRows}
				</tbody>
			</table>
		</div>`;
			}

			// @SERVICE 生成服务HTML
			generateServiceHTML(data) {
				if (!data.agentPerformance) return '';

				const agentRows = data.agentPerformance.map(agent =>
					`<tr>
						<td>${agent.name}</td>
						<td>${agent.totalQuestions}</td>
						<td>${agent.avgSatisfaction.toFixed(1)}%</td>
						<td>${agent.avgEffectiveness.toFixed(1)}%</td>
					</tr>`).join('');

				return `
		<div class="section">
			<h2 class="section-title">${data.title}</h2>
			<div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
				<p style="margin: 0; line-height: 1.8;">${data.analysis}</p>
			</div>
			<h3>客服表现统计</h3>
			<table class="data-table">
				<thead>
					<tr><th>客服姓名</th><th>处理问题数</th><th>平均满意度</th><th>平均有效性</th></tr>
				</thead>
				<tbody>
					${agentRows}
				</tbody>
			</table>
		</div>`;
			}

			// @SERVICE 生成知识库HTML
			generateKnowledgeHTML(data) {
				if (!data) return '';

				const gapRows = (data.knowledgeGaps || []).map(gap =>
					`<tr>
						<td>${gap.category}</td>
						<td>${gap.frequency}</td>
						<td><span class="priority-${gap.priority}">${gap.priority}</span></td>
					</tr>`).join('');

				return `
		<div class="section">
			<h2 class="section-title">${data.title}</h2>
			<div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
				<p style="margin: 0; line-height: 1.8;">${data.analysis}</p>
			</div>
			<div class="metrics-grid">
				<div class="metric-card">
					<div class="metric-value">${data.knowledgeStats.totalItems}</div>
					<div class="metric-label">知识条目</div>
				</div>
				<div class="metric-card">
					<div class="metric-value">${data.knowledgeStats.qaItems}</div>
					<div class="metric-label">问答条目</div>
				</div>
				<div class="metric-card">
					<div class="metric-value">${data.knowledgeStats.coverageRate.toFixed(1)}%</div>
					<div class="metric-label">覆盖率</div>
				</div>
			</div>
			${gapRows ? `
			<h3>知识盲点</h3>
			<table class="data-table">
				<thead>
					<tr><th>问题类型</th><th>频次</th><th>优先级</th></tr>
				</thead>
				<tbody>
					${gapRows}
				</tbody>
			</table>` : ''}
		</div>`;
			}

			// @SERVICE 生成详细记录HTML
			generateDetailsHTML(data) {
				if (!data.recentQuestions) return '';

				const questionRows = data.recentQuestions.slice(0, 10).map(q =>
					`<tr>
						<td>${q.questionBrief}</td>
						<td>${q.supportAgent}</td>
						<td>${q.satisfaction}%</td>
						<td>${q.resolutionRate}%</td>
						<td>${q.tags.join(', ')}</td>
					</tr>`).join('');

				return `
		<div class="section">
			<h2 class="section-title">${data.title}</h2>
			<p>显示最近${data.recentQuestions.length}条记录（共${data.totalRecords}条）</p>
			<table class="data-table">
				<thead>
					<tr><th>问题简述</th><th>客服</th><th>满意度</th><th>解决率</th><th>标签</th></tr>
				</thead>
				<tbody>
					${questionRows}
				</tbody>
			</table>
		</div>`;
			}

			// @SERVICE 生成建议HTML
			generateRecommendationsHTML(data) {
				if (!data.recommendations) return '';

				const recommendationCards = data.recommendations.map(rec =>
					`<div class="recommendation priority-${rec.priority}">
						<div class="recommendation-title">${rec.title}</div>
						<p>${rec.description}</p>
						<ul>
							${rec.actions.map(action => `<li>${action}</li>`).join('')}
						</ul>
					</div>`).join('');

				return `
		<div class="section">
			<h2 class="section-title">${data.title}</h2>
			<p>${data.summary}</p>
			${recommendationCards}
		</div>`;
			}

			// @SERVICE 导出为PDF（简化版本，实际需要PDF库）
			async exportToPDF(reportContent) {
				// 这里应该使用jsPDF或类似库
				// 目前返回一个占位符
				return {
					success: false,
					error: 'PDF导出功能需要额外的库支持'
				};
			}

			// @SERVICE 导出为Word（简化版本，实际需要Word库）
			async exportToWord(reportContent) {
				// 这里应该使用docx.js或类似库
				// 目前返回一个占位符
				return {
					success: false,
					error: 'Word导出功能需要额外的库支持'
				};
			}
		}

		// @UTIL 验证评估结果数据格式
		const validateEvaluationResult = (result) => {
			// 定义必需字段及其类型
			const requiredFields = {
				question: 'string',
				questionBrief: 'string',
				questionTags: 'array',
				supportAgent: 'string',
				responseTime: 'number',
				resolutionRate: 'number',
				effectiveness: 'number',
				efficiency: 'number',
				satisfaction: 'number',
				attitude: 'number',
				knowledge: 'string',
				complexity: 'number',
				priority: 'number'
			};

			// 可选字段（向后兼容）
			const optionalFields = {
				extractedQA: 'object' // 新增：问答题集数据（可选）
			};

			const validated = {};

			// 验证必需字段
			Object.keys(requiredFields).forEach(field => {
				const expectedType = requiredFields[field];
				const value = result[field];

				if (expectedType === 'string') {
					validated[field] = typeof value === 'string' ? value : '';
				} else if (expectedType === 'number') {
					const num = Number(value);
					validated[field] = isNaN(num) ? 0 : Math.max(0, Math.min(100, num));
				} else if (expectedType === 'array') {
					validated[field] = Array.isArray(value) ? value.filter(tag => QUESTION_TAGS.includes(tag)) : [];
				}
			});

			// 验证可选字段（向后兼容）
			Object.keys(optionalFields).forEach(field => {
				const expectedType = optionalFields[field];
				const value = result[field];

				if (value !== undefined) { // 只有当字段存在时才验证
					if (expectedType === 'object' && field === 'extractedQA') {
						// 特殊处理：验证extractedQA对象
						validated[field] = validateExtractedQA(value);
					}
				}
			});

			// 特殊验证：复杂度和优先级应该在1-5范围内
			validated.complexity = Math.max(1, Math.min(5, validated.complexity || 1));
			validated.priority = Math.max(1, Math.min(5, validated.priority || 1));

			// 如果没有提供客服姓名，尝试从原始结果中提取
			if (!validated.supportAgent) {
				validated.supportAgent = '未知客服';
			}

			return validated;
		};

		// 暴露完整校验器到全局，供 llm.js 调用
		window.__validateEvaluationResult__ = validateEvaluationResult;

		// @UTIL 验证问答题集数据格式
		const validateExtractedQA = (qaData) => {
			// 如果没有提供数据或数据为null，返回null
			if (!qaData || typeof qaData !== 'object') {
				return null;
			}

			// 定义问答数据的必需字段
			const qaFields = {
				question: 'string',
				answer: 'string',
				operationSteps: 'array',
				applicableScenarios: 'string',
				difficulty: 'number',
				isCommon: 'boolean'
			};

			const validatedQA = {};

			// 验证每个字段
			Object.keys(qaFields).forEach(field => {
				const expectedType = qaFields[field];
				const value = qaData[field];

				if (expectedType === 'string') {
					validatedQA[field] = typeof value === 'string' ? value : '';
				} else if (expectedType === 'number') {
					const num = Number(value);
					validatedQA[field] = isNaN(num) ? 1 : Math.max(1, Math.min(5, num));
				} else if (expectedType === 'array') {
					validatedQA[field] = Array.isArray(value) ? value.filter(step => typeof step === 'string' && step.trim()) : [];
				} else if (expectedType === 'boolean') {
					validatedQA[field] = Boolean(value);
				}
			});

			// 如果问题或答案为空，返回null（表示没有有效的问答数据）
			if (!validatedQA.question || !validatedQA.answer) {
				return null;
			}

			return validatedQA;
		};

		// @SERVICE 统一的对话分析函数 - 让AI智能判断问题数量并处理
		const evaluateConversationWithKimi = async (conversations, apiKey) => {
			if (!conversations || conversations.length === 0) {
				throw new Error('未找到有效对话内容');
			}

			// 构建完整对话文本，让AI自己判断问题数量
			const conversationText = conversations.map((c) => `${c.role}（${c.name}）: ${c.msg}`).join("\n");

			// 统一的AI分析指令 - 让AI智能判断单问题或多问题
			const prompt = `
你是一名联系中心对话质量分析专家。请分析以下"客服-司机"对话，智能识别其中包含的独立问题数量，并进行深度评估。

【核心任务】
1. 仔细阅读完整对话，识别其中包含的独立问题（一个问题 = 司机的一个具体诉求或关切点）
2. 如果对话只包含一个问题，返回单个JSON对象
3. 如果对话包含多个独立问题，返回JSON数组，每个元素对应一个问题的分析
4. 从每个问题中提取标准化的问答题集数据，构建司机知识库

【问题识别标准】
- 独立问题：司机提出的不同类型的诉求、咨询或投诉
- 相关问题：围绕同一核心问题的补充说明或追问，应合并为一个问题
- 时间跨度：考虑对话的时间跨度，长时间间隔后的新话题通常是新问题

【输入对话】
${conversationText}

【分析要求】
- 深度分析每个问题的背景、客服响应质量、解决效果、司机满意度
- 如果同一问题有多个客服参与，请综合评估团队协作效果
- 重点关注问题的实际解决程度，而非仅仅是回复的及时性
- 提取可操作的实用知识点，帮助其他司机解决类似问题

【问答题集提取要求】
- 识别可复用的通用问题（去除个人信息，如具体订单号、姓名等）
- 将客服回答标准化为清晰的操作步骤
- 只提取有明确解决方案的问答，过滤寒暄和无实质内容的对话
- 确保答案的准确性和可操作性
- 自动判断问题的通用性和重要性

【输出格式】
如果只有一个问题，返回单个JSON对象：
{
  "question": "问题详细描述",
  "questionBrief": "问题简述",
  "questionTags": ["标签1", "标签2"],
  "supportAgent": "主要客服姓名",
  "responseTime": 85,
  "resolutionRate": 90,
  "effectiveness": 88,
  "efficiency": 85,
  "satisfaction": 75,
  "attitude": 92,
  "knowledge": "实用知识点1；知识点2",
  "complexity": 3,
  "priority": 3,
  "extractedQA": {
    "question": "通用问题描述（去除个人信息）",
    "answer": "详细的标准答案",
    "operationSteps": ["步骤1", "步骤2", "步骤3"],
    "applicableScenarios": "适用场景描述",
    "difficulty": 2,
    "isCommon": true
  }
}

如果有多个问题，返回JSON数组：
[
  { /* 第一个问题的完整分析结果，包含extractedQA */ },
  { /* 第二个问题的完整分析结果，包含extractedQA */ },
  ...
]

【字段说明】
- question: string - 司机问题的完整详细描述（20-100字，包含背景和具体诉求）
- questionBrief: string - 问题核心要点简述（8-25字）
- questionTags: array - 问题标签，从以下选择2-5个：["薪资福利","订单派单","车辆维护","路线导航","客户服务","系统操作","政策咨询","投诉建议","技术故障","紧急求助","培训考核","其他"]
- supportAgent: string - 主要处理客服姓名（多客服时选择贡献最大的）
- responseTime: integer - 响应时间评估（0-100）
- resolutionRate: integer - 问题解决完成率（0-100）
- effectiveness: integer - 回复专业性和有效性（0-100）
- efficiency: integer - 沟通效率和流畅度（0-100）
- satisfaction: integer - 司机满意度评估（0-100）
- attitude: integer - 客服服务态度专业性（0-100）
- knowledge: string - 实用知识点，用"；"分隔
- complexity: integer - 问题复杂度（1-5）
- priority: integer - 问题优先级（1-5）
- extractedQA: object - 提取的问答题集数据
  - question: string - 通用问题描述（去除个人信息，如订单号、姓名等）
  - answer: string - 详细的标准答案
  - operationSteps: array - 具体操作步骤数组
  - applicableScenarios: string - 适用场景描述
  - difficulty: integer - 问题难度等级（1-5）
  - isCommon: boolean - 是否为通用问题

【重要提醒】
- 必须基于对话内容客观分析，避免主观臆断
- 评分要充分利用0-100的范围，避免集中在中等水平
- 知识点要具体实用，能真正帮助其他司机
- 严格按照JSON格式输出，不要任何额外文字

【问答提取质量控制】
- 只提取有明确解决方案的问答，过滤纯粹的寒暄或无实质内容的对话
- 确保答案的准确性和可操作性，避免模糊或不完整的回答
- 将个人化信息（如订单号、姓名、具体时间）替换为通用描述
- 如果对话没有实质性的问答内容，extractedQA可以为null

仅输出JSON对象或JSON数组，不要任何额外文字。
`;

			// 统一的API调用和错误处理逻辑
			return await callKimiAPI(prompt, apiKey, '统一对话分析');
		};

		// @SERVICE 统一的Kimi API调用函数已迁移至 llm.js（此处删除内联实现以避免重复定义）

		// @SERVICE 统一的Kimi响应解析函数已迁移至 llm.js（此处删除内联实现以避免重复定义）

		// @SERVICE JSON修复函数已迁移至 llm.js（此处删除内联实现以避免重复定义）

		// @SERVICE 智能处理分析结果已迁移至 llm.js（此处删除内联实现以避免重复定义）

		// @SERVICE 降级方案：逐个分析对话组
		const fallbackToIndividualAnalysis = async (conversationGroups, apiKey) => {
		// 由于已经移除了旧的分组逻辑，这个降级函数不再需要
		// 现在统一使用 evaluateConversationWithKimi 函数
		console.warn('fallbackToIndividualAnalysis 函数已废弃，请使用统一的 evaluateConversationWithKimi 函数');
		throw new Error('fallbackToIndividualAnalysis 函数已废弃');
	};

		// @SERVICE 处理批量分析结果
		const processBatchResults = (results) => {
			let knowledge = [];
			let drivers = {};
			let supportAgents = {}; // 新增：客服统计
			let questionCategories = {};
			let questionTags = {}; // 新增：标签统计
			let metrics = [];
			let detailedQuestions = []; // 新增：详细问题记录
			let qaDataset = []; // 新增：问答题集数据

			// 遍历每个文件的结果
			results.forEach((result) => {
				if (!result || !result.evaluation) return;

				const { fileName, conversations, evaluation } = result;
				const driverName = fileName.replace('.txt', '');

				// 处理多问题结果或单问题结果
				let evaluationsList = [];
				if (evaluation.type === 'multi-question' && evaluation.results) {
					// 多问题结果
					evaluationsList = evaluation.results.map(r => r.evaluation);
				} else {
					// 单问题结果（向后兼容）
					evaluationsList = [evaluation];
				}

				// 处理每个问题的评估结果
				evaluationsList.forEach((singleEvaluation, questionIndex) => {
					const supportAgent = singleEvaluation.supportAgent || '未知客服';

				// 初始化司机数据
				if (!drivers[driverName]) {
					drivers[driverName] = {
						name: driverName,
						conversationCount: 0,
						avgSatisfaction: 0,
						avgEffectiveness: 0,
						avgResolutionRate: 0,
						totalSatisfaction: 0,
						totalEffectiveness: 0,
						totalResolutionRate: 0,
						questionTypes: {},
						lastUpdated: new Date().toISOString()
					};
				}

				// 初始化客服数据
				if (!supportAgents[supportAgent]) {
					supportAgents[supportAgent] = {
						name: supportAgent,
						totalCases: 0,
						avgResponseTime: 0,
						avgResolutionRate: 0,
						avgEffectiveness: 0,
						avgEfficiency: 0,
						avgAttitude: 0,
						totalResponseTime: 0,
						totalResolutionRate: 0,
						totalEffectiveness: 0,
						totalEfficiency: 0,
						totalAttitude: 0,
						handledTags: {},
						complexityCases: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
						priorityCases: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
						lastUpdated: new Date().toISOString()
					};
				}

					// 更新司机统计
					drivers[driverName].conversationCount++;
					drivers[driverName].totalSatisfaction += (singleEvaluation.satisfaction || 0);
					drivers[driverName].totalEffectiveness += (singleEvaluation.effectiveness || 0);
					drivers[driverName].totalResolutionRate += (singleEvaluation.resolutionRate || 0);
					drivers[driverName].avgSatisfaction = drivers[driverName].totalSatisfaction / drivers[driverName].conversationCount;
					drivers[driverName].avgEffectiveness = drivers[driverName].totalEffectiveness / drivers[driverName].conversationCount;
					drivers[driverName].avgResolutionRate = drivers[driverName].totalResolutionRate / drivers[driverName].conversationCount;

					// 更新客服统计
					supportAgents[supportAgent].totalCases++;
					supportAgents[supportAgent].totalResponseTime += (singleEvaluation.responseTime || 0);
					supportAgents[supportAgent].totalResolutionRate += (singleEvaluation.resolutionRate || 0);
					supportAgents[supportAgent].totalEffectiveness += (singleEvaluation.effectiveness || 0);
					supportAgents[supportAgent].totalEfficiency += (singleEvaluation.efficiency || 0);
					supportAgents[supportAgent].totalAttitude += (singleEvaluation.attitude || 0);

					supportAgents[supportAgent].avgResponseTime = supportAgents[supportAgent].totalResponseTime / supportAgents[supportAgent].totalCases;
					supportAgents[supportAgent].avgResolutionRate = supportAgents[supportAgent].totalResolutionRate / supportAgents[supportAgent].totalCases;
					supportAgents[supportAgent].avgEffectiveness = supportAgents[supportAgent].totalEffectiveness / supportAgents[supportAgent].totalCases;
					supportAgents[supportAgent].avgEfficiency = supportAgents[supportAgent].totalEfficiency / supportAgents[supportAgent].totalCases;
					supportAgents[supportAgent].avgAttitude = supportAgents[supportAgent].totalAttitude / supportAgents[supportAgent].totalCases;

					// 统计客服处理的复杂度和优先级分布
					const complexity = singleEvaluation.complexity || 1;
					const priority = singleEvaluation.priority || 1;
					supportAgents[supportAgent].complexityCases[complexity]++;
					supportAgents[supportAgent].priorityCases[priority]++;

					// 添加指标记录
					metrics.push({
						driverName,
						fileName,
						supportAgent,
						questionIndex: evaluation.type === 'multi-question' ? questionIndex : 0,
						responseTime: singleEvaluation.responseTime || 0,
						resolutionRate: singleEvaluation.resolutionRate || 0,
						effectiveness: singleEvaluation.effectiveness || 0,
						efficiency: singleEvaluation.efficiency || 0,
						satisfaction: singleEvaluation.satisfaction || 0,
						attitude: singleEvaluation.attitude || 0,
						complexity: singleEvaluation.complexity || 1,
						priority: singleEvaluation.priority || 1,
						question: singleEvaluation.question || '',
						questionBrief: singleEvaluation.questionBrief || '',
						questionTags: singleEvaluation.questionTags || [],
						timestamp: new Date().toISOString()
					});

					// 记录详细问题
					if (singleEvaluation.question) {
						detailedQuestions.push({
							id: `${driverName}_${questionIndex}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
							question: singleEvaluation.question,
							questionBrief: singleEvaluation.questionBrief || '',
							tags: singleEvaluation.questionTags || [],
							driverName,
							supportAgent,
							resolutionRate: singleEvaluation.resolutionRate || 0,
							effectiveness: singleEvaluation.effectiveness || 0,
							complexity: singleEvaluation.complexity || 1,
							priority: singleEvaluation.priority || 1,
							knowledge: singleEvaluation.knowledge || '',
							source: fileName,
							createdAt: new Date().toISOString()
						});
					}

					// 统计问题标签
					if (singleEvaluation.questionTags && Array.isArray(singleEvaluation.questionTags)) {
						singleEvaluation.questionTags.forEach(tag => {
							questionTags[tag] = (questionTags[tag] || 0) + 1;
							// 更新司机的问题类型统计
							drivers[driverName].questionTypes[tag] = (drivers[driverName].questionTypes[tag] || 0) + 1;
							// 更新客服处理的标签统计
							supportAgents[supportAgent].handledTags[tag] = (supportAgents[supportAgent].handledTags[tag] || 0) + 1;
						});
					}

					// 兼容旧版分类系统
					if (singleEvaluation.questionBrief || singleEvaluation.question) {
						const questionText = (singleEvaluation.questionBrief || singleEvaluation.question || '').toLowerCase();
						let category = '其他问题';

						if (questionText.includes('工资') || questionText.includes('薪资') || questionText.includes('收入')) {
							category = '薪资相关';
						} else if (questionText.includes('订单') || questionText.includes('派单')) {
							category = '订单问题';
						} else if (questionText.includes('车辆') || questionText.includes('维修')) {
							category = '车辆问题';
						} else if (questionText.includes('路线') || questionText.includes('导航')) {
							category = '路线导航';
						} else if (questionText.includes('客户') || questionText.includes('乘客')) {
							category = '客户服务';
						}

						questionCategories[category] = (questionCategories[category] || 0) + 1;
					}

					// 提取知识库条目
					if (singleEvaluation.knowledge && (singleEvaluation.question || singleEvaluation.questionBrief)) {
						const supportMsg = conversations.find(msg => msg.role === 'Support');

						knowledge.push({
							question: singleEvaluation.question || singleEvaluation.questionBrief || '',
							questionBrief: singleEvaluation.questionBrief || '',
							answer: supportMsg?.msg || '',
							knowledge: singleEvaluation.knowledge,
							tags: singleEvaluation.questionTags || [],
							category: '待分类',
							driverName,
							supportAgent,
							source: fileName,
							resolutionRate: singleEvaluation.resolutionRate || 0,
							effectiveness: singleEvaluation.effectiveness || 0,
							complexity: singleEvaluation.complexity || 1,
							priority: singleEvaluation.priority || 1,
							createdAt: new Date().toISOString(),
							frequency: 1
						});
					}

					// 提取问答题集数据（向后兼容）
					try {
						if (singleEvaluation.extractedQA &&
							typeof singleEvaluation.extractedQA === 'object' &&
							singleEvaluation.extractedQA.question &&
							singleEvaluation.extractedQA.answer) {

							const qaData = {
								...singleEvaluation.extractedQA,
								// 添加元数据
								tags: singleEvaluation.questionTags || [],
								sources: [fileName],
								driverName,
								supportAgent,
								originalQuestion: singleEvaluation.question || '',
								resolutionRate: singleEvaluation.resolutionRate || 0,
								effectiveness: singleEvaluation.effectiveness || 0,
								createdAt: new Date().toISOString()
							};

							qaDataset.push(qaData);
							console.log(`📝 提取问答数据: ${qaData.question.substring(0, 50)}...`);
						}
					} catch (qaError) {
						console.warn(`⚠ 问答数据提取失败 (${fileName}):`, qaError);
						// 不影响主流程，继续处理
					}
				}); // 结束evaluationsList.forEach循环
			}); // 结束results.forEach循环

			return {
				knowledge,
				drivers,
				supportAgents, // 新增：客服统计数据
				questionCategories,
				questionTags, // 新增：标签统计
				metrics,
				detailedQuestions, // 新增：详细问题记录
				qaDataset // 新增：问答题集数据
			};
		};

		// 更新仪表盘
		const updateDashboard = () => {
			if (processedFilesEl) {
				processedFilesEl.textContent = processingState.totalStats.processedFiles;
			}
			if (totalMessagesEl) {
				totalMessagesEl.textContent = processingState.totalStats.totalMessages;
			}
			if (totalQuestionsEl) {
				totalQuestionsEl.textContent = processingState.totalStats.totalQuestions;
			}
			if (avgSatisfactionEl) {
				const avgSat = processingState.totalStats.satisfactionCount > 0
					? Math.round(processingState.totalStats.satisfactionSum / processingState.totalStats.satisfactionCount)
					: 0;
				avgSatisfactionEl.textContent = `${avgSat}%`;
			}
		};

		// 图表相关函数已迁移至 charts.js，通过顶部 import 使用

		// 进度更新函数
		const updateProgress = (percent, text, currentFile = '', completed = 0, total = 0) => {
			if (progressFill) {
				progressFill.style.width = `${percent}%`;
			}
			if (progressText) {
				progressText.textContent = text;
			}
			if (progressPercentage) {
				progressPercentage.textContent = `${percent}%`;
			}
			if (currentFileEl && currentFile) {
				currentFileEl.textContent = currentFile;
			}
			if (completedCountEl) {
				completedCountEl.textContent = completed.toString();
			}
			if (totalCountEl) {
				totalCountEl.textContent = total.toString();
			}
		};

		// ==================== 实时结果表格管理 ====================
		
		// 表格元素引用
		const resultsTable = document.getElementById('results-table');
		const resultsTbody = document.getElementById('results-tbody');
		const resultsCount = document.getElementById('results-count');
		const clearResultsBtn = document.getElementById('clear-results-btn');

		// 实时结果数据存储
		let realTimeResults = [];

		// @UTIL 添加处理中状态到表格
		// @UTIL 添加处理中状态到表格（线程安全版本）
		const addProcessingToTable = async (fileName, index) => {
			return await ConcurrencyManager.safeExecute('resultsTable', () => {
				if (!resultsTbody) return;

				// 首次添加时清空占位符
				if (index === 0) {
					resultsTbody.innerHTML = '';
				}

				const driverName = fileName.replace('.txt', '');

				// 创建处理中的表格行
				const row = document.createElement('tr');
				row.id = `processing-row-${index}`;
				row.innerHTML = `
					<td><span class="status-badge status-processing">处理中</span></td>
					<td title="${fileName}">${driverName}</td>
					<td colspan="6" class="text-muted">正在分析对话内容...</td>
				`;

				// 添加到表格顶部
				if (resultsTbody.firstChild) {
					resultsTbody.insertBefore(row, resultsTbody.firstChild);
				} else {
					resultsTbody.appendChild(row);
				}

				// 滚动到最新添加的行
				if (row.scrollIntoView) {
					row.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
				}

				return row;
			});
		};

		// @UTIL 添加新结果到表格（线程安全版本）
		const addResultToTable = async (result, index) => {
			return await ConcurrencyManager.safeExecute('resultsTable', () => {
				if (!resultsTbody || !result) return;

			// 移除对应的处理中行
			const processingRow = document.getElementById(`processing-row-${index}`);
			if (processingRow) {
				processingRow.remove();
			}

			// 解析结果数据
			const fileName = result.fileName || '-';
			const driverName = fileName.replace('.txt', '');
			
			// 格式化数值函数
			const formatScore = (score) => {
				if (typeof score === 'number') {
					const cssClass = score >= 80 ? 'score-high' : score >= 60 ? 'score-medium' : 'score-low';
					return `<span class="${cssClass}">${score}%</span>`;
				}
				return score;
			};

			const formatResponseTime = (time) => {
				if (typeof time === 'number') {
					return time < 60 ? `${time}秒` : `${Math.round(time/60)}分钟`;
				}
				return time;
			};

			// 处理多问题结果 - 为每个问题创建单独的行
			if (result.evaluation && result.evaluation.type === 'multi-question' && result.evaluation.results && result.evaluation.results.length > 0) {
				// 多问题文件 - 为每个问题创建一行
				result.evaluation.results.forEach((questionResult, questionIndex) => {
					const evaluation = questionResult.evaluation || {};
					
					const question = evaluation.question || evaluation.questionBrief || '-';
					const supportAgent = evaluation.supportAgent || '-';
					const effectiveness = evaluation.effectiveness || '-';
					const satisfaction = evaluation.satisfaction || '-';
					const responseTime = evaluation.responseTime || '-';
					const questionTags = (evaluation.questionTags || []).join(', ') || '-';

					// 创建表格行
					const row = document.createElement('tr');
					row.innerHTML = `
						<td><span class="status-badge status-completed">已完成</span></td>
						<td title="${fileName}">${driverName}${result.evaluation.results.length > 1 ? ` (${questionIndex + 1}/${result.evaluation.results.length})` : ''}</td>
						<td class="question-cell" title="${question}">${question}</td>
						<td>${supportAgent}</td>
						<td class="score-cell">${formatScore(effectiveness)}</td>
						<td class="score-cell">${formatScore(satisfaction)}</td>
						<td>${formatResponseTime(responseTime)}</td>
						<td>${questionTags}</td>
					`;

					// 添加到表格顶部（最新结果在上面）
					if (resultsTbody.firstChild) {
						resultsTbody.insertBefore(row, resultsTbody.firstChild);
					} else {
						resultsTbody.appendChild(row);
					}

					// 滚动到最新结果
					if (questionIndex === 0 && row.scrollIntoView) {
						row.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
					}
				});
			} else {
				// 单问题结果
				let question = '-';
				let supportAgent = '-';
				let effectiveness = '-';
				let satisfaction = '-';
				let responseTime = '-';
				let questionTags = '-';

				if (result.evaluation) {
					question = result.evaluation.question || result.evaluation.questionBrief || '-';
					supportAgent = result.evaluation.supportAgent || '-';
					effectiveness = result.evaluation.effectiveness || '-';
					satisfaction = result.evaluation.satisfaction || '-';
					responseTime = result.evaluation.responseTime || '-';
					questionTags = (result.evaluation.questionTags || []).join(', ') || '-';
				}

				// 创建表格行
				const row = document.createElement('tr');
				row.innerHTML = `
					<td><span class="status-badge status-completed">已完成</span></td>
					<td title="${fileName}">${driverName}</td>
					<td class="question-cell" title="${question}">${question}</td>
					<td>${supportAgent}</td>
					<td class="score-cell">${formatScore(effectiveness)}</td>
					<td class="score-cell">${formatScore(satisfaction)}</td>
					<td>${formatResponseTime(responseTime)}</td>
					<td>${questionTags}</td>
				`;

				// 添加到表格顶部（最新结果在上面）
				if (resultsTbody.firstChild) {
					resultsTbody.insertBefore(row, resultsTbody.firstChild);
				} else {
					resultsTbody.appendChild(row);
				}

				// 滚动到最新结果
				if (row.scrollIntoView) {
					row.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
				}
			}

			// 更新结果计数
			realTimeResults.push(result);
			updateResultsCount();

			// 显示清空按钮
			if (clearResultsBtn) {
				clearResultsBtn.style.display = 'inline-block';
			}
			});
		};

		// @UTIL 更新结果计数
		const updateResultsCount = () => {
			if (resultsCount) {
				resultsCount.textContent = `${realTimeResults.length} 条记录`;
			}
		};

		// @UTIL 清空结果表格
		const clearResultsTable = () => {
			if (resultsTbody) {
				resultsTbody.innerHTML = `
					<tr>
						<td colspan="8" class="text-muted" style="text-align: center; padding: var(--spacing-xl);">
							暂无数据，开始分析后将实时显示结果
						</td>
					</tr>
				`;
			}
			realTimeResults = [];
			updateResultsCount();
			if (clearResultsBtn) {
				clearResultsBtn.style.display = 'none';
			}
		};

		// @UTIL 实时更新图表数据
		const updateChartsRealTime = () => {
			if (!globalCharts || !appDataManager) return;

			try {
				updateChartsData(globalCharts, appDataManager.data);
				updateAllChartPreviews(appDataManager.data);
			} catch (error) {
				console.warn('实时图表更新失败:', error);
			}
		};

		// ==================== 图表预览表格管理 ====================

		// @UTIL 更新所有图表预览表格
		const updateAllChartPreviews = (appData) => {
			updateQuestionsPreview(appData);
			updateEffectivenessPreview(appData);
			updateSatisfactionPreview(appData);
			updateKnowledgePreview(appData);

			// 更新主统计概览
			updateMainStatsOverview();

			// 更新问答题集图表
			updateQACharts();
		};

		// @UTIL 更新问题分布预览表格
		const updateQuestionsPreview = (appData) => {
			const tbody = document.getElementById('questions-preview-tbody');
			if (!tbody) return;

			// 统计问题类别分布
			const categoryStats = {};
			let totalQuestions = 0;

			// 从详细问题记录中统计
			if (appData.detailedQuestions && appData.detailedQuestions.length > 0) {
				appData.detailedQuestions.forEach(item => {
					const tags = item.questionTags || ['其他'];
					tags.forEach(tag => {
						if (!categoryStats[tag]) {
							categoryStats[tag] = { count: 0, latest: '' };
						}
						categoryStats[tag].count++;
						categoryStats[tag].latest = item.questionBrief || item.question || '';
						totalQuestions++;
					});
				});
			}

			if (totalQuestions === 0) {
				tbody.innerHTML = '<tr><td colspan="4" class="no-data">暂无数据</td></tr>';
				return;
			}

			// 生成表格行
			const rows = Object.entries(categoryStats)
				.sort(([,a], [,b]) => b.count - a.count)
				.slice(0, 5) // 只显示前5个
				.map(([category, stats]) => {
					const percentage = ((stats.count / totalQuestions) * 100).toFixed(1);
					return `
						<tr>
							<td>${category}</td>
							<td>${stats.count}</td>
							<td>${percentage}%</td>
							<td title="${stats.latest}">${stats.latest.substring(0, 20)}${stats.latest.length > 20 ? '...' : ''}</td>
						</tr>
					`;
				}).join('');

			tbody.innerHTML = rows;
		};

		// @UTIL 更新有效性预览表格
		const updateEffectivenessPreview = (appData) => {
			const tbody = document.getElementById('effectiveness-preview-tbody');
			if (!tbody) return;

			// 统计司机有效性数据
			const driverStats = {};

			if (appData.drivers && Object.keys(appData.drivers).length > 0) {
				Object.entries(appData.drivers).forEach(([driverName, data]) => {
					const questions = data.questions || [];
					if (questions.length > 0) {
						const avgEffectiveness = questions.reduce((sum, q) => sum + (q.effectiveness || 0), 0) / questions.length;
						const avgResponseTime = questions.reduce((sum, q) => sum + (q.responseTime || 0), 0) / questions.length;
						
						driverStats[driverName] = {
							effectiveness: Math.round(avgEffectiveness),
							responseTime: Math.round(avgResponseTime),
							questionCount: questions.length
						};
					}
				});
			}

			if (Object.keys(driverStats).length === 0) {
				tbody.innerHTML = '<tr><td colspan="4" class="no-data">暂无数据</td></tr>';
				return;
			}

			// 生成表格行
			const rows = Object.entries(driverStats)
				.sort(([,a], [,b]) => b.effectiveness - a.effectiveness)
				.slice(0, 5)
				.map(([driver, stats]) => {
					const effectivenessClass = stats.effectiveness >= 80 ? 'score-high' : stats.effectiveness >= 60 ? 'score-medium' : 'score-low';
					const responseText = stats.responseTime < 60 ? `${stats.responseTime}秒` : `${Math.round(stats.responseTime/60)}分钟`;
					
					return `
						<tr>
							<td>${driver}</td>
							<td><span class="${effectivenessClass}">${stats.effectiveness}%</span></td>
							<td>${responseText}</td>
							<td>${stats.questionCount}</td>
						</tr>
					`;
				}).join('');

			tbody.innerHTML = rows;
		};

		// @UTIL 更新满意度预览表格
		const updateSatisfactionPreview = (appData) => {
			const tbody = document.getElementById('satisfaction-preview-tbody');
			if (!tbody) return;

			// 统计司机满意度数据
			const satisfactionData = [];

			if (appData.drivers && Object.keys(appData.drivers).length > 0) {
				Object.entries(appData.drivers).forEach(([driverName, data]) => {
					const questions = data.questions || [];
					questions.forEach(question => {
						if (question.satisfaction !== undefined) {
							satisfactionData.push({
								driver: driverName,
								satisfaction: question.satisfaction,
								supportAgent: question.supportAgent || '未知',
								question: question.questionBrief || question.question || ''
							});
						}
					});
				});
			}

			if (satisfactionData.length === 0) {
				tbody.innerHTML = '<tr><td colspan="4" class="no-data">暂无数据</td></tr>';
				return;
			}

			// 生成表格行（按满意度排序）
			const rows = satisfactionData
				.sort((a, b) => b.satisfaction - a.satisfaction)
				.slice(0, 5)
				.map(item => {
					const satisfactionClass = item.satisfaction >= 80 ? 'score-high' : item.satisfaction >= 60 ? 'score-medium' : 'score-low';
					
					return `
						<tr>
							<td>${item.driver}</td>
							<td><span class="${satisfactionClass}">${item.satisfaction}%</span></td>
							<td>${item.supportAgent}</td>
							<td title="${item.question}">${item.question.substring(0, 15)}${item.question.length > 15 ? '...' : ''}</td>
						</tr>
					`;
				}).join('');

			tbody.innerHTML = rows;
		};

		// @UTIL 更新知识库预览表格
		const updateKnowledgePreview = (appData) => {
			const tbody = document.getElementById('knowledge-preview-tbody');
			if (!tbody) return;

			const knowledgeItems = appData.knowledge || [];

			if (knowledgeItems.length === 0) {
				tbody.innerHTML = '<tr><td colspan="4" class="no-data">暂无数据</td></tr>';
				return;
			}

			// 生成表格行（显示最新的知识点）
			const rows = knowledgeItems
				.slice(-5) // 显示最新的5条
				.reverse()
				.map(item => {
					const complexityText = ['', '简单', '一般', '中等', '复杂', '疑难'][item.complexity || 1];
					
					return `
						<tr>
							<td>${item.category || '其他'}</td>
							<td title="${item.knowledge}">${item.knowledge ? item.knowledge.substring(0, 30) : ''}${item.knowledge && item.knowledge.length > 30 ? '...' : ''}</td>
							<td>${complexityText}</td>
							<td>${item.sourceFile || '-'}</td>
						</tr>
					`;
				}).join('');

			tbody.innerHTML = rows;
		};

		// ==================== DRAG-UPLOAD.JS ====================
		/**
		 * drag-upload.js - 拖拽上传功能模块
		 * @COMPONENT 拖拽上传功能模块
		 * 创建日期: 2024-06-13
		 */

		// @COMPONENT 拖拽上传管理器
		class DragUploadManager {
			constructor(dropAreaSelector, fileListSelector, fileInputSelector) {
				this.dropArea = document.querySelector(dropAreaSelector);
				this.fileList = document.querySelector(fileListSelector);
				this.fileInput = document.querySelector(fileInputSelector);
				this.selectedFiles = [];

				this.init();
			}

			// @LIFECYCLE 初始化拖拽功能
			init() {
				if (!this.dropArea) {
					console.error('拖拽区域未找到');
					return;
				}

				// @EVENT_HANDLER 拖拽事件监听
				this.dropArea.addEventListener('dragover', this.handleDragOver.bind(this));
				this.dropArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
				this.dropArea.addEventListener('drop', this.handleDrop.bind(this));

				// @EVENT_HANDLER 点击上传事件
				this.dropArea.addEventListener('click', () => {
					if (this.fileInput) {
						this.fileInput.click();
					}
				});

				// @EVENT_HANDLER 文件选择事件
				if (this.fileInput) {
					this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
				}

				console.log('拖拽上传功能初始化完成');
			}

			// @EVENT_HANDLER 处理拖拽悬停
			handleDragOver(e) {
				e.preventDefault();
				this.dropArea.classList.add('drag-over');
			}

			// @EVENT_HANDLER 处理拖拽离开
			handleDragLeave(e) {
				e.preventDefault();
				this.dropArea.classList.remove('drag-over');
			}

			// @EVENT_HANDLER 处理文件拖拽放置（支持文件夹拖拽）
			async handleDrop(e) {
				e.preventDefault();
				this.dropArea.classList.remove('drag-over');

				const dt = e.dataTransfer;
				let files = Array.from(dt.files || []);

				// 优先尝试通过 DataTransferItem 遍历目录
				if (dt.items && dt.items.length > 0 && typeof dt.items[0].webkitGetAsEntry === 'function') {
					try {
						const entries = Array.from(dt.items)
							.filter(item => item.kind === 'file')
							.map(item => item.webkitGetAsEntry())
							.filter(Boolean);

						const collected = await this.collectDroppedFiles(entries);
						if (collected && collected.length > 0) {
							files = collected;
						}
					} catch (err) {
						console.warn('目录遍历失败，回退到直接文件列表:', err);
					}
				}

				this.addFiles(files);
			}

			// @UTIL 遍历拖拽的条目，收集文件
			async collectDroppedFiles(entries) {
				const results = [];
				for (const entry of entries) {
					const files = await this.traverseEntry(entry);
					results.push(...files);
				}
				return results;
			}

			// @UTIL 递归遍历文件/目录条目
			async traverseEntry(entry) {
				if (!entry) return [];
				if (entry.isFile) {
					const file = await new Promise((resolve, reject) => {
						try { entry.file(resolve, reject); } catch (e) { reject(e); }
					});
					return [file];
				}
				if (entry.isDirectory) {
					const reader = entry.createReader();
					const readAll = async () => {
						const all = [];
						while (true) {
							const batch = await new Promise((resolve, reject) => {
								reader.readEntries(resolve, reject);
							});
							if (!batch || batch.length === 0) break;
							all.push(...batch);
						}
						return all;
					};
					const children = await readAll();
					let collected = [];
					for (const child of children) {
						const nested = await this.traverseEntry(child);
						collected.push(...nested);
					}
					return collected;
				}
				return [];
			}

			// @EVENT_HANDLER 处理文件选择
			handleFileSelect(e) {
				const files = Array.from(e.target.files);
				this.addFiles(files);
			}

			// @SERVICE 添加文件到选择列表
			addFiles(files) {
				const txtFiles = files.filter(file =>
					file.name.toLowerCase().endsWith('.txt') && file.size > 0
				);

				if (txtFiles.length === 0) {
					alert('请选择有效的 .txt 文件');
					return;
				}

				// 避免重复添加相同文件
				txtFiles.forEach(file => {
					const exists = this.selectedFiles.some(existing =>
						existing.name === file.name && existing.size === file.size
					);

					if (!exists) {
						this.selectedFiles.push(file);
					}
				});

				this.updateFileList();
				this.updateDropAreaText();
				updateStartButtonState();
			}

			// @SERVICE 移除文件
			removeFile(index) {
				this.selectedFiles.splice(index, 1);
				this.updateFileList();
				this.updateDropAreaText();
				updateStartButtonState();
			}

			// @SERVICE 清空所有文件
			clearFiles() {
				this.selectedFiles = [];
				this.updateFileList();
				this.updateDropAreaText();
				updateStartButtonState();
			}

			// @SERVICE 更新文件列表显示
			updateFileList() {
				if (!this.fileList) return;

				if (this.selectedFiles.length === 0) {
					this.fileList.innerHTML = '<p class="text-muted">暂无已选择文件</p>';
					updateStartButtonState();
					return;
				}

				const listHTML = this.selectedFiles.map((file, index) => `
					<div class="file-item d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
						<div>
							<strong>${file.name}</strong>
							<small class="text-muted d-block">${this.formatFileSize(file.size)}</small>
						</div>
						<button type="button" class="btn btn-sm btn-outline-danger" onclick="dragUploadManager.removeFile(${index})">
							移除
						</button>
					</div>
				`).join('');

				this.fileList.innerHTML = listHTML;
				updateStartButtonState();
			}

			// @SERVICE 更新拖拽区域文本
			updateDropAreaText() {
				const textElement = this.dropArea.querySelector('p');
				if (textElement) {
					if (this.selectedFiles.length === 0) {
						textElement.innerHTML = '拖拽 .txt 文件到此处，或点击选择文件<br><small class="text-muted">支持多文件同时上传</small>';
					} else {
						textElement.innerHTML = `已选择 ${this.selectedFiles.length} 个文件<br><small class="text-muted">继续拖拽或点击添加更多文件</small>`;
					}
				}
			}

			// @UTIL 格式化文件大小
			formatFileSize(bytes) {
				if (bytes === 0) return '0 Bytes';
				const k = 1024;
				const sizes = ['Bytes', 'KB', 'MB', 'GB'];
				const i = Math.floor(Math.log(bytes) / Math.log(k));
				return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
			}

			// @SERVICE 获取选择的文件
			getSelectedFiles() {
				return this.selectedFiles;
			}

			// @SERVICE 获取文件数量
			getFileCount() {
				return this.selectedFiles.length;
			}
		}

		// 显示/隐藏进度条
		const showProgress = (show) => {
			if (progressCard) {
				progressCard.style.display = show ? 'block' : 'none';
			}
			if (show) {
				pauseBtn.style.display = 'inline-block';
				resumeBtn.style.display = 'none';
			} else {
				pauseBtn.style.display = 'none';
				resumeBtn.style.display = 'none';
			}
		};



		// 文件验证函数
		const validateFile = (file) => {
			const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
			if (fileExtension !== '.txt') {
				return { valid: false, error: `文件 "${file.name}" 不是TXT格式` };
			}
			
			const maxSize = 10 * 1024 * 1024; // 10MB
			if (file.size > maxSize) {
				return { valid: false, error: `文件 "${file.name}" 超过10MB限制` };
			}
			
			return { valid: true };
		};

		// 处理文件选择
		const handleFileSelect = (files) => {
			selectedFiles = [];
			const errors = [];
			let folderPath = '';
			
			Array.from(files).forEach(file => {
				const validation = validateFile(file);
				if (validation.valid) {
					selectedFiles.push(file);
					// 保存文件夹路径（从第一个文件中提取）
					if (!folderPath && file.webkitRelativePath) {
						folderPath = file.webkitRelativePath.substring(0, file.webkitRelativePath.lastIndexOf('/'));
					}
				} else {
					if (!validation.error.includes('不是TXT格式')) {
						errors.push(validation.error);
					}
				}
			});
			
			// 持久化文件夹路径
			if (folderPath && appDataManager) {
				appDataManager.storage.save('lastSelectedFolder', folderPath);
				log(`已记住文件夹路径: ${folderPath}`);
			}
			
			if (errors.length > 0) {
				alert(errors.join('\n'));
			}
			
			log(`已选择 ${selectedFiles.length} 个有效的TXT文件`);
			
			// 重置处理状态
			processingState = {
				isRunning: false,
				isPaused: false,
				currentIndex: 0,
				results: [],
				totalStats: {
					processedFiles: 0,
					totalMessages: 0,
					totalQuestions: 0,
					satisfactionSum: 0,
					satisfactionCount: 0
				}
			};
			updateDashboard();
			
			updateStartButtonState();
		};

		// 读取文件内容
		const readFileContent = (file) => {
			return new Promise((resolve, reject) => {
				const reader = new FileReader();
				reader.onload = (e) => resolve(e.target.result);
				reader.onerror = () => reject(new Error('文件读取失败'));
				reader.readAsText(file, 'utf-8');
			});
		};


		


		// ==================== MAIN.JS 核心处理逻辑 ====================

		// @SERVICE 处理文件（更新版：支持多问题分析）
		const processFile = async (file, index, total) => {
			try {
				// @REALTIME 添加处理中状态到表格
				await addProcessingToTable(file.name, index);
				
				log(`正在处理: ${file.name} (${index + 1}/${total})`);

				// @STEP 解析文件内容
				const parseResult = await parseConversationFile(file);
				const { conversations } = parseResult;

				if (conversations.length === 0) {
					log(`${file.name}: 未找到有效对话`);
					return null;
				}

				log(`${file.name}: 发现 ${conversations.length} 条对话记录，将由AI智能判断问题数量`);

				// @STEP AI评估（支持多问题分析）
				let evaluation;
				try {
					// 优先使用配置的API Key
					const apiKey = CONFIG.apiKey || await loadApiKey();

					if (!apiKey || CONFIG.useSimulation) {
						log(`使用模拟评估模式处理文件: ${file.name}`);
						evaluation = await simulateAIEvaluation(conversations);
					} else {
						log(`使用Kimi API智能分析文件: ${file.name}`);
						evaluation = await evaluateConversationWithKimi(conversations, apiKey);
					}
				} catch (apiError) {
					log(`API调用失败，降级为模拟模式: ${apiError.message}`);
					evaluation = await simulateAIEvaluation(conversations);
				}

				// @STEP 更新处理状态和仪表盘
				processingState.totalStats.processedFiles++;
				processingState.totalStats.totalMessages += conversations.length;

				// 计算问题数量（支持多问题结果）
				let questionCount = 1;
				if (evaluation.type === 'multi-question' && evaluation.results) {
					questionCount = evaluation.results.length;
				}
				processingState.totalStats.totalQuestions += questionCount;

				// 计算平均满意度（支持多问题结果）
				if (evaluation.type === 'multi-question' && evaluation.results) {
					evaluation.results.forEach(result => {
						if (result.evaluation && result.evaluation.satisfaction) {
							processingState.totalStats.satisfactionSum += result.evaluation.satisfaction;
							processingState.totalStats.satisfactionCount++;
						}
					});
				} else if (evaluation.satisfaction) {
					processingState.totalStats.satisfactionSum += evaluation.satisfaction;
					processingState.totalStats.satisfactionCount++;
				}

				// 实时更新仪表盘
				updateDashboard();

				const progress = Math.round(((index + 1) / total) * 100);
				updateProgress(
					progress,
					`已处理: ${file.name} (${index + 1}/${total})`,
					file.name,
					index + 1,
					total
				);

				const result = {
					fileName: file.name,
					conversations: conversations,
					// groups 字段已移除，现在由AI智能判断问题数量
					evaluation: evaluation
				};

				// 保存到结果数组
				processingState.results.push(result);

				// @REALTIME 实时更新表格和图表
				await addResultToTable(result, index);
				
				// 更新数据管理器以便图表能获取最新数据
				if (appDataManager && result.evaluation) {
					try {
						const batchData = processBatchResults([result]);
						
						// 增量更新数据管理器
						Object.assign(appDataManager.data.drivers, batchData.drivers);
						Object.assign(appDataManager.data.supportAgents, batchData.supportAgents);
						appDataManager.data.knowledge.push(...batchData.knowledge);
						Object.assign(appDataManager.data.questionCategories, batchData.questionCategories);
						Object.assign(appDataManager.data.questionTags, batchData.questionTags);
						appDataManager.data.metrics.push(...batchData.metrics);
						appDataManager.data.detailedQuestions.push(...batchData.detailedQuestions);

						// 处理问答题集数据
						if (batchData.qaDataset && batchData.qaDataset.length > 0) {
							batchData.qaDataset.forEach(qaData => {
								appDataManager.addQAItem(qaData);
							});
						}
						
						// 实时更新图表
						updateChartsRealTime();
					} catch (updateError) {
						console.warn('实时数据更新失败:', updateError);
					}
				}

				return result;

			} catch (error) {
				log(`处理文件 ${file.name} 时出错: ${error.message}`);
				
				// @REALTIME 更新表格状态为错误
				const processingRow = document.getElementById(`processing-row-${index}`);
				if (processingRow) {
					processingRow.innerHTML = `
						<td><span class="status-badge status-error">错误</span></td>
						<td title="${file.name}">${file.name.replace('.txt', '')}</td>
						<td colspan="6" class="text-muted">处理失败: ${error.message}</td>
					`;
				}
				
				return null;
			}
		};

		// @SERVICE 并发处理文件，避免数据冲突和重复
		const processConcurrentFiles = async (files, maxConcurrency) => {
			console.log(`🚀 [并发处理] 开始处理 ${files.length} 个文件，最大并发数: ${maxConcurrency}`);
			console.log(`📁 [并发处理] 文件列表:`, files.map((file, index) => ({
				index,
				name: file.name,
				size: `${(file.size / 1024).toFixed(1)}KB`,
				type: file.type || 'text/plain'
			})));
			
			const results = [];
			const processedCount = { value: 0 };
			const dataLock = { locked: false }; // 简单的数据锁，防止并发写入冲突
			let isFirstFile = true; // 标记是否是第一个文件，用于清空表格
			const startTime = Date.now();
			
			// 创建处理单个文件的包装函数，确保数据同步
			const processFileWrapper = async (file, index) => {
				const fileStartTime = Date.now();
				console.log(`📄 [并发处理] 开始处理文件 ${index + 1}/${files.length}: ${file.name}`);
				
				try {
					// 检查是否被停止
					if (!processingState.isRunning) {
						console.log(`⏹️ [并发处理] 处理已停止，跳过文件: ${file.name}`);
						return null;
					}
					
					// 确保第一个文件能清空表格
					if (isFirstFile) {
						console.log(`🧹 [并发处理] 首个文件，准备清空表格`);
						isFirstFile = false;
					}
					
					// 使用唯一的索引避免冲突
					const result = await processFile(file, index, files.length);
					const fileProcessTime = Date.now() - fileStartTime;
					
					console.log(`✅ [并发处理] 文件 ${index + 1} 处理完成: ${file.name}，耗时: ${fileProcessTime}ms`);
					if (result) {
						console.log(`📊 [并发处理] 文件 ${index + 1} 结果:`, {
							conversationCount: result.conversations?.length || 0,
							groupCount: result.evaluations?.length || 0,
							hasData: !!result.conversations && result.conversations.length > 0
						});
					}
					
					// 使用锁保护共享状态更新
					return await ConcurrencyManager.safeExecute('processingState', () => {
						// 原子性更新进度计数器
						processedCount.value++;
						
						console.log(`📈 [并发处理] 进度更新: ${processedCount.value}/${files.length} (${Math.round((processedCount.value / files.length) * 100)}%)`);
						
						if (result) {
							results[index] = result; // 使用索引确保顺序
							// 更新全局状态（线程安全）
							processingState.results = results.filter(r => r !== null);
							console.log(`💾 [并发处理] 结果已保存到索引 ${index}，当前有效结果数: ${processingState.results.length}`);
						}
						
						// 更新进度
						const progress = Math.round((processedCount.value / files.length) * 100);
						updateProgress(progress, `处理中... (${processedCount.value}/${files.length})`, '', processedCount.value, files.length);
						
						return result;
					});
					
				} catch (error) {
					const fileErrorTime = Date.now() - fileStartTime;
					console.error(`❌ [并发处理] 文件 ${index + 1} 处理失败: ${file.name}，耗时: ${fileErrorTime}ms，错误:`, error.message);
					console.error(`🔍 [并发处理] 错误详情:`, error);
					await ConcurrencyManager.safeExecute('processingState', () => {
						processedCount.value++;
					});
					log(`并发处理文件 ${file.name} 时出错: ${error.message}`);
					return null;
				}
			};
			
			// 分批并发处理，避免同时发送过多请求
			for (let i = 0; i < files.length; i += maxConcurrency) {
				// 检查是否需要暂停
				while (processingState.isPaused && processingState.isRunning) {
					await new Promise(resolve => setTimeout(resolve, 100));
				}
				
				// 检查是否被停止
				if (!processingState.isRunning) {
					log('并发处理被停止');
					break;
				}
				
				const batch = files.slice(i, i + maxConcurrency);
				const batchPromises = batch.map((file, batchIndex) => {
					const globalIndex = i + batchIndex;
					return processFileWrapper(file, globalIndex);
				});
				
				log(`开始处理批次 ${Math.floor(i/maxConcurrency) + 1}/${Math.ceil(files.length/maxConcurrency)}：${batch.length} 个文件`);
				
				// 等待当前批次完成
				await Promise.all(batchPromises);
				
				// 批次间短暂延迟，避免API限流
				if (i + maxConcurrency < files.length) {
					await new Promise(resolve => setTimeout(resolve, 200));
				}
			}
			
			return results.filter(r => r !== null);
		};

		// 主处理函数（重构为使用高并发队列管理器）
		const handleFiles = async () => {
			console.log(`🎯 [主处理] 开始文件处理流程`);

			if (selectedFiles.length === 0) {
				console.warn(`⚠️ [主处理] 未选择任何文件`);
				alert('请先选择文件');
				return;
			}

			console.log(`📋 [主处理] 已选择 ${selectedFiles.length} 个文件`);
			console.log(`📁 [主处理] 文件清单:`, selectedFiles.map((file, index) => ({
				index,
				name: file.name,
				size: `${(file.size / 1024).toFixed(1)}KB`,
				lastModified: new Date(file.lastModified).toLocaleString()
			})));

			// 检查是否有可用的AI引擎
			if (apiConfigManager && dualLLMManager) {
				const availableEngines = apiConfigManager.getAvailableEngines();
				if (availableEngines.length === 0) {
					console.warn(`⚠️ [主处理] 没有可用的AI引擎`);
					if (!confirm('没有配置可用的AI引擎，将使用模拟数据进行演示。是否继续？')) {
						console.log(`🚫 [主处理] 用户取消了模拟数据处理`);
						return;
					}
					console.log(`✅ [主处理] 用户确认使用模拟数据继续`);
				} else {
					console.log(`🤖 [主处理] 可用AI引擎:`, availableEngines.map(e => e.name));
				}
			} else {
				console.warn(`⚠️ [主处理] 高并发管理器未初始化，使用传统模式`);
			}

			// 检查是否有未完成的会话
			if (progressManager && progressManager.hasUnfinishedSession()) {
				const unfinishedSession = progressManager.resumeSession();
				const resume = confirm(
					`检测到未完成的分析会话 (${unfinishedSession.sessionId})。\n` +
					`进度: ${unfinishedSession.processedFiles}/${unfinishedSession.totalFiles}\n` +
					`是否继续之前的分析？`
				);

				if (resume) {
					console.log('🔄 恢复之前的分析会话');
					showProcessingStatus(); // 显示状态面板
					if (queueManager) {
						await queueManager.resumeProcessing();
					}
					return;
				} else {
					// 清理旧会话
					progressManager.cleanupOldSessions();
				}
			}

			console.log(`🚀 [主处理] 初始化处理状态...`);
			processingState.isRunning = true;
			processingState.isPaused = false;
			processingState.currentIndex = 0;

			// 清空之前的结果
			console.log(`🧹 [主处理] 清空上次处理结果`);
			clearResultsTable();

			startBtn.disabled = true;
			showProgress(true);
			updateProgress(0, '开始处理...', '', 0, selectedFiles.length);

			// 显示处理状态面板
			showProcessingStatus();

			const mainStartTime = Date.now();

			try {
				// 使用高并发队列管理器处理文件
				if (queueManager) {
					console.log(`🔄 [主处理] 使用高并发队列管理器处理...`);

					// 添加文件到队列
					const jobs = await queueManager.addFiles(selectedFiles);

					if (jobs.length === 0) {
						console.warn(`⚠️ [主处理] 所有文件都已处理过，跳过重复文件`);
						log('⚠ 所有文件都已处理过，如需重新分析请清空队列后重试');
						return;
					}

					console.log(`📋 [主处理] 队列中有 ${jobs.length} 个新文件待处理`);
					log(`开始高并发处理 ${jobs.length} 个文件（最大并发数：${queueManager.maxConcurrency}）...`);

					// 启动队列处理
					await queueManager.startProcessing();

					// 获取处理结果
					const queueStatus = queueManager.getQueueStatus();
					console.log(`📊 [主处理] 队列处理完成:`, queueStatus);

					if (queueStatus.completed === 0) {
						console.warn(`⚠️ [主处理] 没有成功处理的文件`);
						log('⚠ 没有文件被成功处理，请检查文件格式和API配置');
						return;
					}

					// 收集所有完成的结果
					const allResults = queueManager.completedJobs
						.map(job => ({
							fileName: job.file.name,
							conversations: [], // 这里需要从结果中重构对话数据
							evaluationResults: job.result
						}))
						.filter(result => result.evaluationResults && result.evaluationResults.length > 0);

					console.log(`📋 [主处理] 收集到 ${allResults.length} 个有效分析结果`);

					// 使用收集到的结果继续后续处理
					const results = allResults;

				} else {
					// 回退到传统并发处理
					console.log(`🔄 [主处理] 使用传统并发处理...`);
					log(`开始并发处理 ${selectedFiles.length} 个文件（最大并发数：${MAX_CONCURRENCY}）...`);
					const results = await processConcurrentFiles(selectedFiles, MAX_CONCURRENCY);
				}
				
				const mainProcessTime = Date.now() - mainStartTime;
				console.log(`⏱️ [主处理] 总处理时间: ${mainProcessTime}ms (${(mainProcessTime / 1000).toFixed(1)}秒)`);
				
				if (processingState.isRunning) {
					console.log(`🎉 [主处理] 处理完成，开始生成统计报告...`);
					console.log(`📈 [主处理] 处理统计:`, {
						totalFiles: selectedFiles.length,
						processedFiles: processingState.totalStats.processedFiles,
						successRate: `${((processingState.totalStats.processedFiles / selectedFiles.length) * 100).toFixed(1)}%`,
						totalMessages: processingState.totalStats.totalMessages,
						totalQuestions: processingState.totalStats.totalQuestions,
						avgProcessingTime: `${(mainProcessTime / selectedFiles.length).toFixed(0)}ms/文件`
					});
					
					updateProgress(100, '处理完成！', '', selectedFiles.length, selectedFiles.length);
					log(`=== 分析完成 ===`);
					log(`处理文件: ${processingState.totalStats.processedFiles} 个`);
					log(`对话记录: ${processingState.totalStats.totalMessages} 条`);
					log(`识别问题: ${processingState.totalStats.totalQuestions} 个`);

					const avgSat = processingState.totalStats.satisfactionCount > 0
						? Math.round(processingState.totalStats.satisfactionSum / processingState.totalStats.satisfactionCount)
						: 0;
					log(`平均满意度: ${avgSat}%`);

					// @STEP 处理批量结果并保存到数据管理器
					if (appDataManager && results.filter(r => r !== null).length > 0) {
						console.log(`💾 [主处理] 开始保存分析结果...`);
						log('正在保存分析结果到本地存储...');
						const validResults = results.filter(r => r !== null);
						console.log(`📊 [主处理] 有效结果数: ${validResults.length}/${results.length}`);
						
						const batchData = processBatchResults(validResults);
						console.log(`📋 [主处理] 批量数据统计:`, {
							drivers: Object.keys(batchData.drivers).length,
							supportAgents: Object.keys(batchData.supportAgents).length,
							knowledge: batchData.knowledge.length,
							questionCategories: Object.keys(batchData.questionCategories).length,
							metrics: batchData.metrics.length,
							qaDataset: batchData.qaDataset ? batchData.qaDataset.length : 0
						});

						// 更新数据管理器
						console.log(`🔄 [主处理] 更新数据管理器...`);
						Object.assign(appDataManager.data.drivers, batchData.drivers);
						Object.assign(appDataManager.data.supportAgents, batchData.supportAgents);
						appDataManager.data.knowledge.push(...batchData.knowledge);
						Object.assign(appDataManager.data.questionCategories, batchData.questionCategories);
						Object.assign(appDataManager.data.questionTags, batchData.questionTags);
						appDataManager.data.metrics.push(...batchData.metrics);
						appDataManager.data.detailedQuestions.push(...batchData.detailedQuestions);

						// 批量处理问答题集数据
						if (batchData.qaDataset && batchData.qaDataset.length > 0) {
							console.log(`📝 [主处理] 处理问答题集数据: ${batchData.qaDataset.length} 条`);
							const qaResult = appDataManager.addQAItems(batchData.qaDataset);
							console.log(`✅ [主处理] 问答题集处理完成: ${qaResult.addedCount} 条`);
						}

						// 保存到本地存储
						console.log(`💿 [主处理] 保存到本地存储...`);
						const saveSuccess = appDataManager.saveAllData();
						if (saveSuccess) {
							console.log(`✅ [主处理] 本地存储保存成功`);
							log('✓ 分析结果已保存到本地存储');

							// 更新主统计概览
							updateMainStatsOverview();
						} else {
							console.error(`❌ [主处理] 本地存储保存失败`);
							log('⚠ 保存到本地存储时出现问题');
						}

						// 更新图表
						console.log(`📊 [主处理] 更新图表显示...`);
						if (globalCharts) {
							log('正在更新图表...');
							updateChartsData(globalCharts, appDataManager.data);
							updateAllChartPreviews(appDataManager.data);
							log('✓ 图表更新完成');
						}
					}

					log(`=== 详细结果 ===`);
					processingState.results.forEach(result => {
						log(`📄 ${result.fileName}:`);
						if (result.evaluation && result.evaluation.type === 'multi-question') {
							log(`  - 对话记录: ${result.conversations.length} 条`);
							log(`  - 问题数量: ${result.evaluation.results.length} 个`);
						} else {
							log(`  - 对话记录: ${result.conversations.length} 条`);
							// 问题数量现在由AI智能判断，不再使用本地分组
							log(`  - 问题数量: 由AI智能判断`);
						}
					});

					// 生成最终报告
					generateReport();

					setTimeout(() => {
						showProgress(false);
						updateProgress(0, '等待开始...', '', 0, 0);
					}, 3000);
				}
				
			} catch (error) {
				const mainErrorTime = Date.now() - mainStartTime;
				console.error(`❌ [主处理] 处理过程中发生错误，耗时: ${mainErrorTime}ms`);
				console.error(`🔍 [主处理] 错误详情:`, {
					message: error.message,
					stack: error.stack,
					name: error.name,
					processedFiles: processingState.totalStats.processedFiles,
					currentIndex: processingState.currentIndex
				});
				console.error(`📊 [主处理] 错误时的处理状态:`, {
					isRunning: processingState.isRunning,
					isPaused: processingState.isPaused,
					totalStats: processingState.totalStats,
					resultsCount: processingState.results.length
				});
				
				log(`处理过程中发生错误: ${error.message}`);
				showProgress(false);
			} finally {
				const finalTime = Date.now() - mainStartTime;
				console.log(`🏁 [主处理] 处理流程结束，总耗时: ${finalTime}ms`);
				console.log(`📊 [主处理] 最终统计:`, {
					processedFiles: processingState.totalStats.processedFiles,
					successfulResults: processingState.results.length,
					totalMessages: processingState.totalStats.totalMessages,
					totalQuestions: processingState.totalStats.totalQuestions
				});
				
				processingState.isRunning = false;
				processingState.isPaused = false;
				startBtn.disabled = false;
			}
		};

		// 暂停处理
		const pauseProcessing = () => {
			processingState.isPaused = true;
			pauseBtn.style.display = 'none';
			resumeBtn.style.display = 'inline-block';
			log('处理已暂停');
		};

		// 恢复处理
		const resumeProcessing = () => {
			processingState.isPaused = false;
			pauseBtn.style.display = 'inline-block';
			resumeBtn.style.display = 'none';
			log('处理已恢复');
		};

		// @SERVICE 生成最终报告
		const generateReport = () => {
			if (!processingState.results || processingState.results.length === 0) {
				log('没有可用的分析结果');
				return;
			}

			log('=== 📊 分析报告 ===');

			// 总体统计
			const totalFiles = processingState.results.length;
			const totalConversations = processingState.results.reduce((sum, r) => sum + r.conversations.length, 0);
			const totalQuestions = processingState.totalStats.totalQuestions;

			log(`📁 处理文件: ${totalFiles} 个`);
			log(`💬 对话记录: ${totalConversations} 条`);
			log(`❓ 识别问题: ${totalQuestions} 个`);

			// 满意度统计
			if (processingState.totalStats.satisfactionCount > 0) {
				const avgSatisfaction = Math.round(
					processingState.totalStats.satisfactionSum / processingState.totalStats.satisfactionCount
				);
				log(`😊 平均满意度: ${avgSatisfaction}%`);
			}

			// 数据存储状态
			if (appDataManager) {
				const stats = appDataManager.getStatsSummary();
				log(`💾 数据存储状态:`);
				log(`  - 司机档案: ${stats.totalDrivers} 个`);
				log(`  - 客服档案: ${stats.totalSupportAgents} 个`);
				log(`  - 知识库: ${stats.totalKnowledge} 条`);
				log(`  - 问题标签: ${stats.totalTags} 种`);
				log(`  - 存储大小: ${stats.storageInfo.totalSizeFormatted || '未知'}`);
			}

			log('=== 报告生成完成 ===');
		};

		// @SERVICE 模拟AI评估函数（兼容性保留）
		const simulateAIEvaluation = async (conversations) => {
			// 模拟API延迟
			await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

			// 生成模拟数据
			const mockEvaluation = {
				question: "模拟问题：" + (conversations[0]?.msg || "司机咨询").substring(0, 30),
				questionBrief: "模拟问题简述",
				questionTags: ["其他"],
				supportAgent: "模拟客服",
				responseTime: Math.floor(Math.random() * 100),
				resolutionRate: Math.floor(Math.random() * 100),
				effectiveness: Math.floor(Math.random() * 100),
				efficiency: Math.floor(Math.random() * 100),
				satisfaction: Math.floor(Math.random() * 100),
				attitude: Math.floor(Math.random() * 100),
				knowledge: "模拟知识点",
				complexity: Math.floor(Math.random() * 5) + 1,
				priority: Math.floor(Math.random() * 5) + 1
			};

			return mockEvaluation;
		};

		// @LIFECYCLE 应用初始化函数
		const initializeApp = async () => {
			log('正在初始化应用...');

			// @INIT 初始化数据管理器
			appDataManager = new AppDataManager();
			log('✓ 数据管理器初始化完成');

			// @INIT 初始化高并发处理管理器
			try {
				fileRegistry = new FileRegistry();
				progressManager = new AnalysisProgressManager();
				apiConfigManager = new APIConfigManager();
				dualLLMManager = new DualLLMManager();
				queueManager = new ConcurrentQueueManager(10);
				reportGenerator = new ReportGenerator();
				reportGenerator.initializeLLMManager(dualLLMManager);

				// 初始化问答优化管理器
				initializeQAOptimization();

				// 加载外部API配置
				await apiConfigManager.loadExternalConfig();

				log('✓ 高并发处理管理器初始化完成');
				console.log('🚀 支持的AI引擎:', apiConfigManager.getAvailableEngines().map(e => e.name));
			} catch (error) {
				console.error('❌ 高并发处理管理器初始化失败:', error);
				log('⚠ 高并发处理管理器初始化失败，将使用基础模式');
			}

			// @INIT 初始化拖拽上传管理器
			dragUploadManager = new DragUploadManager('#drag-drop-area', '#file-list', '#file-input');
			log('✓ 拖拽上传管理器初始化完成');

			// @INIT 初始化图表
			try {
				globalCharts = await initializeCharts();
				if (globalCharts) {
					await setupCharts(globalCharts);
					// 加载现有数据到图表
					updateChartsData(globalCharts, appDataManager.data);
					updateAllChartPreviews(appDataManager.data);
					log('✓ 图表初始化完成');
				} else {
					log('⚠ 图表初始化失败，但不影响核心功能');
				}
			} catch (chartError) {
				console.error('图表初始化错误:', chartError);
				log('⚠ 图表功能不可用，但不影响数据处理');
			}

			// @INIT 显示应用统计信息
			const stats = appDataManager.getStatsSummary();
			log(`📊 当前数据统计:`);
			log(`  - 司机数据: ${stats.totalDrivers} 个`);
			log(`  - 客服数据: ${stats.totalSupportAgents} 个`);
			log(`  - 知识库条目: ${stats.totalKnowledge} 个`);
			log(`  - 问题标签: ${stats.totalTags} 个`);
			log(`  - 详细问题记录: ${stats.totalDetailedQuestions} 个`);

			log('🚀 应用初始化完成，可以开始上传文件');
		};

		// @EVENT_HANDLER 事件监听器设置
		const setupEventListeners = () => {
			// 初始化状态UI
			initializeStatusUI();

			// 开始处理按钮
			if (startBtn) {
				startBtn.addEventListener('click', () => {
					const files = dragUploadManager ? dragUploadManager.getSelectedFiles() : selectedFiles;
					if (files.length > 0) {
						selectedFiles = files; // 同步到全局变量以保持兼容性
						handleFiles();
					} else {
						alert('请先选择文件');
					}
				});
			}

			// 显示状态按钮
			const showStatusBtn = document.getElementById('show-status-btn');
			if (showStatusBtn) {
				showStatusBtn.addEventListener('click', () => {
					showProcessingStatus();
				});
			}

			// 暂停/恢复按钮
			if (pauseBtn) {
				pauseBtn.addEventListener('click', pauseProcessing);
			}
			if (resumeBtn) {
				resumeBtn.addEventListener('click', resumeProcessing);
			}

			// 清空结果表格按钮
			if (clearResultsBtn) {
				clearResultsBtn.addEventListener('click', () => {
					if (confirm('确定要清空所有结果数据吗？')) {
						clearResultsTable();
						log('结果表格已清空');
					}
				});
			}

			// 标签页切换事件
			setupTabNavigation();

			// 问答题集相关事件
			setupQADatasetEvents();

			// 报告功能相关事件
			setupReportEvents();

			log('✓ 事件监听器设置完成');
		};

		// @EVENT_HANDLER 标签页导航设置
		const setupTabNavigation = () => {
			const tabBtns = document.querySelectorAll('.tab-btn');
			const tabContents = document.querySelectorAll('.tab-content');

			tabBtns.forEach(btn => {
				btn.addEventListener('click', () => {
					const targetTab = btn.getAttribute('data-tab');

					// 移除所有活跃状态
					tabBtns.forEach(b => b.classList.remove('active'));
					tabContents.forEach(c => c.classList.remove('active'));

					// 激活当前标签
					btn.classList.add('active');
					const targetContent = document.getElementById(`tab-${targetTab}`);
					if (targetContent) {
						targetContent.classList.add('active');

						// 如果切换到问答题集标签，刷新数据
						if (targetTab === 'qa-dataset') {
							refreshQADataset();
						}
					}
				});
			});
		};

		// @EVENT_HANDLER 问答题集事件设置
		const setupQADatasetEvents = () => {
			// 搜索功能
			const searchInput = document.getElementById('qa-search');
			if (searchInput) {
				searchInput.addEventListener('input', debounce(() => {
					filterQADataset();
				}, 300));
			}

			// 智能优化按钮
			const optimizeBtn = document.getElementById('qa-optimize-btn');
			if (optimizeBtn) {
				optimizeBtn.addEventListener('click', handleQAOptimization);
			}

			// 筛选功能
			const difficultyFilter = document.getElementById('qa-difficulty-filter');
			const commonFilter = document.getElementById('qa-common-filter');
			const sortBy = document.getElementById('qa-sort-by');

			if (difficultyFilter) {
				difficultyFilter.addEventListener('change', filterQADataset);
			}
			if (commonFilter) {
				commonFilter.addEventListener('change', filterQADataset);
			}
			if (sortBy) {
				sortBy.addEventListener('change', filterQADataset);
			}

			// 导出功能
			const exportBtn = document.getElementById('qa-export-btn');
			if (exportBtn) {
				exportBtn.addEventListener('click', toggleExportDropdown);
			}

			// 导出选项事件
			const exportOptions = document.querySelectorAll('.export-option');
			exportOptions.forEach(option => {
				option.addEventListener('click', handleExportOption);
			});

			// 点击外部关闭下拉菜单
			document.addEventListener('click', (e) => {
				const dropdown = document.querySelector('.export-dropdown');
				if (dropdown && !dropdown.contains(e.target)) {
					dropdown.classList.remove('active');
				}
			});
		};

		// @UTIL 防抖函数
		const debounce = (func, wait) => {
			let timeout;
			return function executedFunction(...args) {
				const later = () => {
					clearTimeout(timeout);
					func(...args);
				};
				clearTimeout(timeout);
				timeout = setTimeout(later, wait);
			};
		};

		// @LIFECYCLE 页面加载完成后初始化
		document.addEventListener('DOMContentLoaded', async () => {
			console.log(`🎉 =================================`);
			console.log(`🚀 司机客服对话分析系统 v2.0 启动`);
			console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
			console.log(`🌐 用户代理: ${navigator.userAgent}`);
			console.log(`📱 屏幕分辨率: ${screen.width}x${screen.height}`);
			console.log(`🎉 =================================`);
			
			log('🚀 司机客服对话分析系统启动中...');

			try {
				console.log(`📦 [系统] 开始初始化应用组件...`);
				
				// @INIT 初始化应用
				await initializeApp();
				console.log(`✅ [系统] 应用初始化完成`);

				// @INIT 设置事件监听器
				console.log(`🔗 [系统] 设置事件监听器...`);
				setupEventListeners();
				console.log(`✅ [系统] 事件监听器设置完成`);

				// @INIT 初始化仪表盘
				console.log(`📊 [系统] 初始化仪表盘...`);
				updateDashboard();
				console.log(`✅ [系统] 仪表盘初始化完成`);

				// @INIT API配置提示
				if (!CONFIG.apiKey || CONFIG.apiKey === 'sk-your-kimi-api-key') {
					console.warn(`⚠️ [系统] 当前使用模拟模式，未配置有效API Key`);
					log('⚠ 提示: 当前使用模拟模式');
					log('如需使用真实AI分析，请配置有效的 Kimi API Key');
				} else {
					console.log(`✅ [系统] 已配置API Key，将使用真实AI分析`);
					log('✓ 已配置API Key，将使用真实AI分析');
				}

				console.log(`🎉 [系统] 系统初始化完成，所有组件就绪`);
				log('✅ 系统初始化完成，可以开始上传文件进行分析');

				// 初始化统计概览
				updateMainStatsOverview();

				// 显示调试提示
				showDebugTips();

			} catch (error) {
				console.error(`❌ [系统] 应用初始化失败:`, error);
				console.error(`🔍 [系统] 错误详情:`, {
					message: error.message,
					stack: error.stack,
					name: error.name
				});
				console.error('应用初始化失败:', error);
				log('❌ 应用初始化失败: ' + error.message);
			}
		}); // End of DOMContentLoaded event listener

		// ==================== QA DATASET MANAGEMENT ====================
		/**
		 * 问答题集管理功能
		 * @SERVICE 问答题集管理功能
		 */

		// @SERVICE 刷新问答题集显示
		const refreshQADataset = () => {
			if (!appDataManager) return;

			try {
				const stats = appDataManager.getQADatasetStats();
				updateQAStats(stats);

				const qaData = appDataManager.getQADataset();
				renderQAList(qaData);
			} catch (error) {
				console.warn('问答题集刷新失败:', error);
				// 显示错误状态
				const container = document.getElementById('qa-list-container');
				if (container) {
					container.innerHTML = `
						<div class="qa-empty-state">
							<i class="fas fa-exclamation-triangle"></i>
							<p>数据加载失败</p>
							<p class="text-muted">请刷新页面重试</p>
						</div>
					`;
				}
			}
		};

		// @SERVICE 更新问答统计信息
		const updateQAStats = (stats) => {
			const totalCountEl = document.getElementById('qa-total-count');
			const commonCountEl = document.getElementById('qa-common-count');
			const avgDifficultyEl = document.getElementById('qa-avg-difficulty');
			const avgFrequencyEl = document.getElementById('qa-avg-frequency');

			if (totalCountEl) totalCountEl.textContent = stats.total || 0;
			if (commonCountEl) commonCountEl.textContent = stats.commonQuestions || 0;
			if (avgDifficultyEl) avgDifficultyEl.textContent = (stats.avgFrequency || 0).toFixed(1);
			if (avgFrequencyEl) avgFrequencyEl.textContent = (stats.avgFrequency || 0).toFixed(1);
		};

		// @SERVICE 渲染问答列表（支持分页优化）
		const renderQAList = (qaData) => {
			const container = document.getElementById('qa-list-container');
			if (!container) return;

			if (!qaData || qaData.length === 0) {
				container.innerHTML = `
					<div class="qa-empty-state">
						<i class="fas fa-question-circle"></i>
						<p>暂无问答数据</p>
						<p class="text-muted">上传对话文件进行分析后，系统会自动提取问答数据</p>
					</div>
				`;
				return;
			}

			// 性能优化：大量数据时使用分页
			const pageSize = 20; // 每页显示20条
			const totalPages = Math.ceil(qaData.length / pageSize);

			if (qaData.length > pageSize) {
				// 显示分页版本
				renderQAListWithPagination(qaData, container, pageSize, totalPages);
			} else {
				// 直接渲染所有数据
				const qaItems = qaData.map(qa => createQAItem(qa)).join('');
				container.innerHTML = qaItems;
			}
		};

		// @SERVICE 渲染带分页的问答列表
		const renderQAListWithPagination = (qaData, container, pageSize, totalPages) => {
			let currentPage = 1;

			const renderPage = (page) => {
				const startIndex = (page - 1) * pageSize;
				const endIndex = startIndex + pageSize;
				const pageData = qaData.slice(startIndex, endIndex);

				const qaItems = pageData.map(qa => createQAItem(qa)).join('');
				const paginationHtml = createPaginationHtml(page, totalPages, qaData.length);

				container.innerHTML = `
					<div class="qa-pagination-info">
						<span>显示 ${startIndex + 1}-${Math.min(endIndex, qaData.length)} 条，共 ${qaData.length} 条记录</span>
					</div>
					${qaItems}
					${paginationHtml}
				`;

				// 绑定分页事件
				bindPaginationEvents(renderPage, totalPages);
			};

			// 渲染第一页
			renderPage(currentPage);
		};

		// @UTIL 创建分页HTML
		const createPaginationHtml = (currentPage, totalPages, totalItems) => {
			if (totalPages <= 1) return '';

			let paginationHtml = '<div class="qa-pagination">';

			// 上一页按钮
			if (currentPage > 1) {
				paginationHtml += `<button class="pagination-btn" data-page="${currentPage - 1}">上一页</button>`;
			}

			// 页码按钮
			const startPage = Math.max(1, currentPage - 2);
			const endPage = Math.min(totalPages, currentPage + 2);

			if (startPage > 1) {
				paginationHtml += `<button class="pagination-btn" data-page="1">1</button>`;
				if (startPage > 2) {
					paginationHtml += `<span class="pagination-ellipsis">...</span>`;
				}
			}

			for (let i = startPage; i <= endPage; i++) {
				const activeClass = i === currentPage ? ' active' : '';
				paginationHtml += `<button class="pagination-btn${activeClass}" data-page="${i}">${i}</button>`;
			}

			if (endPage < totalPages) {
				if (endPage < totalPages - 1) {
					paginationHtml += `<span class="pagination-ellipsis">...</span>`;
				}
				paginationHtml += `<button class="pagination-btn" data-page="${totalPages}">${totalPages}</button>`;
			}

			// 下一页按钮
			if (currentPage < totalPages) {
				paginationHtml += `<button class="pagination-btn" data-page="${currentPage + 1}">下一页</button>`;
			}

			paginationHtml += '</div>';
			return paginationHtml;
		};

		// @EVENT_HANDLER 绑定分页事件
		const bindPaginationEvents = (renderPage, totalPages) => {
			const paginationBtns = document.querySelectorAll('.pagination-btn');
			paginationBtns.forEach(btn => {
				btn.addEventListener('click', (e) => {
					const page = parseInt(e.target.getAttribute('data-page'));
					if (page >= 1 && page <= totalPages) {
						renderPage(page);
						// 滚动到顶部
						document.getElementById('qa-list-container').scrollTop = 0;
					}
				});
			});
		};

		// @UTIL 创建问答项HTML
		const createQAItem = (qa) => {
			const difficultyText = ['', '简单', '一般', '中等', '困难', '复杂'][qa.difficulty || 1];
			const stepsHtml = qa.operationSteps && qa.operationSteps.length > 0
				? `<div class="qa-steps">
					<div class="qa-steps-title">操作步骤：</div>
					<ol class="qa-steps-list">
						${qa.operationSteps.map(step => `<li>${step}</li>`).join('')}
					</ol>
				</div>`
				: '';

			return `
				<div class="qa-item" data-qa-id="${qa.id}">
					<div class="qa-item-header">
						<div class="qa-question">${qa.question}</div>
						<div class="qa-meta">
							<span class="qa-badge difficulty-${qa.difficulty || 1}">${difficultyText}</span>
							${qa.isCommon ? '<span class="qa-badge common">通用</span>' : ''}
							<span class="qa-badge">频次: ${qa.frequency || 1}</span>
						</div>
					</div>
					<div class="qa-item-body">
						<div class="qa-answer">${qa.answer}</div>
						${stepsHtml}
						${qa.applicableScenarios ? `<div><strong>适用场景：</strong>${qa.applicableScenarios}</div>` : ''}
					</div>
					<div class="qa-item-footer">
						<div>
							<span>创建时间：${qa.createdAt ? new Date(qa.createdAt).toLocaleDateString() : '未知'}</span>
							${qa.sources && qa.sources.length > 0 ? `<span> | 来源：${qa.sources.join(', ')}</span>` : ''}
						</div>
						<div>
							<span>更新时间：${qa.lastUpdated ? new Date(qa.lastUpdated).toLocaleDateString() : '未知'}</span>
						</div>
					</div>
				</div>
			`;
		};

		// @SERVICE 筛选问答数据
		const filterQADataset = () => {
			if (!appDataManager) return;

			const searchTerm = document.getElementById('qa-search')?.value || '';
			const difficulty = document.getElementById('qa-difficulty-filter')?.value || '';
			const isCommon = document.getElementById('qa-common-filter')?.value || '';
			const sortBy = document.getElementById('qa-sort-by')?.value || 'frequency';

			const filters = {
				keyword: searchTerm,
				sortBy: sortBy
			};

			if (difficulty) {
				filters.difficulty = parseInt(difficulty);
			}

			if (isCommon !== '') {
				filters.isCommon = isCommon === 'true';
			}

			const filteredData = appDataManager.getQADataset(filters);
			renderQAList(filteredData);
		};

		// @EVENT_HANDLER 切换导出下拉菜单
		const toggleExportDropdown = (e) => {
			e.preventDefault();
			e.stopPropagation();

			const dropdown = document.querySelector('.export-dropdown');
			if (dropdown) {
				dropdown.classList.toggle('active');
			}
		};

		// @EVENT_HANDLER 处理导出选项点击
		const handleExportOption = (e) => {
			e.preventDefault();

			const format = e.currentTarget.getAttribute('data-format');
			const type = e.currentTarget.getAttribute('data-type');

			// 关闭下拉菜单
			const dropdown = document.querySelector('.export-dropdown');
			if (dropdown) {
				dropdown.classList.remove('active');
			}

			// 处理不同的导出选项
			switch (format) {
				case 'csv':
					exportQADataset('csv', type);
					break;
				case 'json':
					exportQADataset('json', type);
					break;
				case 'template':
					downloadImportTemplate();
					break;
				default:
					console.warn('未知的导出格式:', format);
			}
		};

		// @SERVICE 导出问答数据
		const exportQADataset = (format = 'csv', type = 'filtered') => {
			if (!appDataManager) {
				alert('数据管理器未初始化');
				return;
			}

			let filters = {};

			// 根据导出类型设置筛选条件
			if (type === 'filtered') {
				const searchTerm = document.getElementById('qa-search')?.value || '';
				const difficulty = document.getElementById('qa-difficulty-filter')?.value || '';
				const isCommon = document.getElementById('qa-common-filter')?.value || '';

				if (searchTerm) filters.keyword = searchTerm;
				if (difficulty) filters.difficulty = parseInt(difficulty);
				if (isCommon !== '') filters.isCommon = isCommon === 'true';
			}
			// type === 'all' 时不设置筛选条件，导出全部数据

			let content, mimeType, fileExtension;

			if (format === 'csv') {
				content = appDataManager.exportQADatasetCSV(filters);
				mimeType = 'text/csv;charset=utf-8;';
				fileExtension = 'csv';
			} else if (format === 'json') {
				content = exportQADatasetJSON(filters);
				mimeType = 'application/json;charset=utf-8;';
				fileExtension = 'json';
			}

			if (!content) {
				alert('没有数据可导出');
				return;
			}

			// 创建下载链接
			const blob = new Blob([content], { type: mimeType });
			const link = document.createElement('a');
			const url = URL.createObjectURL(blob);

			const typeLabel = type === 'all' ? '全部' : '筛选';
			const fileName = `问答题集_${typeLabel}_${new Date().toISOString().split('T')[0]}.${fileExtension}`;

			link.setAttribute('href', url);
			link.setAttribute('download', fileName);
			link.style.visibility = 'hidden';
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);

			log(`✓ 问答题集数据已导出 (${format.toUpperCase()}, ${typeLabel})`);
		};

		// @SERVICE 导出问答数据为JSON
		const exportQADatasetJSON = (filters = {}) => {
			if (!appDataManager) return null;

			const dataset = appDataManager.getQADataset(filters);

			if (dataset.length === 0) {
				return null;
			}

			const exportData = {
				exportInfo: {
					timestamp: new Date().toISOString(),
					totalCount: dataset.length,
					filters: filters,
					version: '1.0'
				},
				qaDataset: dataset
			};

			return JSON.stringify(exportData, null, 2);
		};

		// @SERVICE 下载导入模板
		const downloadImportTemplate = () => {
			const templateData = {
				templateInfo: {
					name: '问答题集导入模板',
					version: '1.0',
					description: '用于批量导入问答题集数据的模板文件',
					fields: {
						question: '问题描述（必填）',
						answer: '详细答案（必填）',
						operationSteps: '操作步骤，用分号分隔（可选）',
						applicableScenarios: '适用场景描述（可选）',
						difficulty: '难度等级，1-5（可选，默认为2）',
						isCommon: '是否为通用问题，true/false（可选，默认为true）',
						tags: '标签，用分号分隔（可选）'
					}
				},
				sampleData: [
					{
						question: '如何查看订单状态？',
						answer: '您可以在APP首页点击"我的订单"查看当前订单状态',
						operationSteps: '打开APP;点击首页;选择我的订单;查看订单详情',
						applicableScenarios: '司机需要查看当前订单进度时',
						difficulty: 1,
						isCommon: true,
						tags: '订单查询;基础操作'
					},
					{
						question: '订单延迟如何处理？',
						answer: '遇到订单延迟时，请先刷新订单列表，如仍有问题请联系客服',
						operationSteps: '下拉刷新订单列表;检查网络连接;联系在线客服;提供订单号',
						applicableScenarios: '订单显示延迟或状态异常时',
						difficulty: 2,
						isCommon: true,
						tags: '订单问题;故障处理'
					}
				]
			};

			const content = JSON.stringify(templateData, null, 2);
			const blob = new Blob([content], { type: 'application/json;charset=utf-8;' });
			const link = document.createElement('a');
			const url = URL.createObjectURL(blob);

			link.setAttribute('href', url);
			link.setAttribute('download', `问答题集导入模板_${new Date().toISOString().split('T')[0]}.json`);
			link.style.visibility = 'hidden';
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);

			log('✓ 导入模板已下载');
		};

		// @SERVICE 更新主统计概览
		const updateMainStatsOverview = () => {
			if (!appDataManager) return;

			try {
				const stats = appDataManager.getStatsSummary();
				const qaStats = appDataManager.getQADatasetStats();

				// 更新基础统计
				updateElementText('main-drivers-count', stats.totalDrivers || 0);
				updateElementText('main-agents-count', stats.totalSupportAgents || 0);
				updateElementText('main-knowledge-count', stats.totalKnowledge || 0);
				updateElementText('main-qa-count', qaStats.total || 0);

				// 计算平均值
				const metrics = appDataManager.data.metrics || [];
				if (metrics.length > 0) {
					const avgSatisfaction = metrics.reduce((sum, m) => sum + (m.satisfaction || 0), 0) / metrics.length;
					const avgEffectiveness = metrics.reduce((sum, m) => sum + (m.effectiveness || 0), 0) / metrics.length;
					const avgResponseTime = metrics.reduce((sum, m) => sum + (m.responseTime || 0), 0) / metrics.length;

					updateElementText('main-avg-satisfaction', avgSatisfaction.toFixed(1));
					updateElementText('main-avg-effectiveness', avgEffectiveness.toFixed(1));
					updateElementText('main-avg-response-time', avgResponseTime.toFixed(1));
				} else {
					// 没有数据时显示默认值
					updateElementText('main-avg-satisfaction', '0');
					updateElementText('main-avg-effectiveness', '0');
					updateElementText('main-avg-response-time', '0');
				}

				updateElementText('main-common-qa-count', qaStats.commonQuestions || 0);
			} catch (error) {
				console.warn('主统计概览更新失败:', error);
				// 设置默认值
				updateElementText('main-drivers-count', '0');
				updateElementText('main-agents-count', '0');
				updateElementText('main-knowledge-count', '0');
				updateElementText('main-qa-count', '0');
				updateElementText('main-avg-satisfaction', '0');
				updateElementText('main-avg-effectiveness', '0');
				updateElementText('main-avg-response-time', '0');
				updateElementText('main-common-qa-count', '0');
			}
		};

		// @UTIL 更新元素文本内容
		const updateElementText = (elementId, text) => {
			const element = document.getElementById(elementId);
			if (element) {
				element.textContent = text;
			}
		};

		// @SERVICE 更新问答题集图表
		const updateQACharts = () => {
			if (!appDataManager || !window.Plotly) return;

			try {
				const qaStats = appDataManager.getQADatasetStats();

				// 更新难度分布图表
				updateQADifficultyChart(qaStats);

				// 更新标签分布图表
				updateQATagsChart(qaStats);
			} catch (error) {
				console.warn('问答题集图表更新失败:', error);
				// 不影响主流程
			}
		};

		// @EVENT_HANDLER 处理问答智能优化
		const handleQAOptimization = async () => {
			if (!qaOptimizationManager || !appDataManager) {
				alert('优化系统未初始化');
				return;
			}

			const qaDataset = appDataManager.data.qaDataset;
			if (!qaDataset || qaDataset.length === 0) {
				alert('没有问答数据需要优化');
				return;
			}

			// 确认优化操作
			const confirmed = confirm(`即将对 ${qaDataset.length} 条问答数据进行智能优化，包括：\n\n1. 自动标签分类\n2. 去重和合并相似问题\n3. 质量评估和优化\n\n此操作可能需要几分钟时间，是否继续？`);

			if (!confirmed) return;

			// 检查是否正在处理
			if (qaOptimizationManager.isProcessing) {
				alert('优化正在进行中，请稍后再试');
				return;
			}

			try {
				// 显示优化进度对话框
				showOptimizationProgressDialog();

				// 执行优化
				const result = await qaOptimizationManager.optimizeQADataset(qaDataset, {
					progressCallback: updateOptimizationProgress
				});

				// 隐藏进度对话框
				hideOptimizationProgressDialog();

				if (result.success) {
					// 更新数据
					appDataManager.data.qaDataset = result.optimized.items;
					appDataManager.saveData();

					// 刷新显示
					refreshQADataset();
					updateQACharts();

					// 显示结果
					showOptimizationResultDialog(result);
				} else {
					alert(`优化失败: ${result.error}`);
				}

			} catch (error) {
				console.error('智能优化失败:', error);
				alert(`优化失败: ${error.message}`);
				hideOptimizationProgressDialog();
			}
		};

		// @SERVICE 显示优化进度对话框
		const showOptimizationProgressDialog = () => {
			const dialog = document.createElement('div');
			dialog.id = 'optimization-progress-dialog';
			dialog.className = 'modal-overlay';
			dialog.style.cssText = `
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(0,0,0,0.5);
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 1000;
			`;

			dialog.innerHTML = `
				<div class="modal-dialog" style="background: white; border-radius: 8px; padding: 30px; max-width: 500px; width: 90%;">
					<div class="modal-header" style="text-align: center; margin-bottom: 20px;">
						<h3 style="margin: 0; color: #007bff;">
							<i class="fas fa-magic"></i> 智能优化进行中
						</h3>
					</div>
					<div class="modal-body">
						<div class="progress-container" style="margin-bottom: 20px;">
							<div class="progress-bar" style="width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden;">
								<div id="optimization-progress-fill" style="height: 100%; background: linear-gradient(90deg, #007bff, #0056b3); width: 0%; transition: width 0.3s ease;"></div>
							</div>
							<div style="display: flex; justify-content: space-between; margin-top: 8px; font-size: 14px;">
								<span id="optimization-current-step">准备中...</span>
								<span id="optimization-progress-percentage">0%</span>
							</div>
						</div>
						<div class="progress-details" style="background: #f8f9fa; padding: 15px; border-radius: 6px; font-size: 13px; color: #666;">
							<div><strong>当前阶段:</strong> <span id="optimization-stage">初始化</span></div>
							<div style="margin-top: 5px;"><strong>处理状态:</strong> <span id="optimization-status">准备开始</span></div>
						</div>
					</div>
					<div class="modal-footer" style="text-align: center; margin-top: 20px;">
						<button type="button" id="cancel-optimization-btn" class="btn-sm btn-outline-secondary">取消优化</button>
					</div>
				</div>
			`;

			document.body.appendChild(dialog);

			// 取消按钮事件
			const cancelBtn = document.getElementById('cancel-optimization-btn');
			if (cancelBtn) {
				cancelBtn.addEventListener('click', () => {
					if (qaOptimizationManager) {
						qaOptimizationManager.cancelOptimization();
					}
					hideOptimizationProgressDialog();
				});
			}
		};

		// @SERVICE 更新优化进度
		const updateOptimizationProgress = (progress) => {
			const progressFill = document.getElementById('optimization-progress-fill');
			const progressPercentage = document.getElementById('optimization-progress-percentage');
			const currentStep = document.getElementById('optimization-current-step');
			const stage = document.getElementById('optimization-stage');
			const status = document.getElementById('optimization-status');

			if (progressFill) {
				progressFill.style.width = `${progress.percentage}%`;
			}

			if (progressPercentage) {
				progressPercentage.textContent = `${progress.percentage}%`;
			}

			if (currentStep) {
				currentStep.textContent = progress.step || '处理中...';
			}

			if (stage) {
				stage.textContent = progress.stage === 'tagging' ? '标签分析' :
								   progress.stage === 'deduplication' ? '去重优化' : '处理中';
			}

			if (status) {
				status.textContent = `${progress.current || 0}/${progress.total || 0}`;
			}
		};

		// @SERVICE 隐藏优化进度对话框
		const hideOptimizationProgressDialog = () => {
			const dialog = document.getElementById('optimization-progress-dialog');
			if (dialog && dialog.parentNode) {
				dialog.parentNode.removeChild(dialog);
			}
		};

		// @SERVICE 显示优化结果对话框
		const showOptimizationResultDialog = (result) => {
			const dialog = document.createElement('div');
			dialog.className = 'modal-overlay';
			dialog.style.cssText = `
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(0,0,0,0.5);
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 1000;
			`;

			dialog.innerHTML = `
				<div class="modal-dialog" style="background: white; border-radius: 8px; padding: 30px; max-width: 600px; width: 90%;">
					<div class="modal-header" style="text-align: center; margin-bottom: 20px;">
						<h3 style="margin: 0; color: #28a745;">
							<i class="fas fa-check-circle"></i> 优化完成
						</h3>
					</div>
					<div class="modal-body">
						<div class="result-summary" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
							<div class="result-item" style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 6px;">
								<div style="font-size: 24px; font-weight: bold; color: #007bff;">${result.original.count}</div>
								<div style="font-size: 14px; color: #666;">原始数量</div>
							</div>
							<div class="result-item" style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 6px;">
								<div style="font-size: 24px; font-weight: bold; color: #28a745;">${result.optimized.count}</div>
								<div style="font-size: 14px; color: #666;">优化后数量</div>
							</div>
						</div>
						<div class="result-details" style="background: #e8f5e8; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
							<div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
								<span>减少数量:</span>
								<span style="font-weight: bold; color: #28a745;">${result.statistics.duplicatesRemoved} 条</span>
							</div>
							<div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
								<span>减少比例:</span>
								<span style="font-weight: bold; color: #28a745;">${result.statistics.reductionRate}%</span>
							</div>
							<div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
								<span>标签化处理:</span>
								<span style="font-weight: bold; color: #007bff;">${result.statistics.tagged} 条</span>
							</div>
							<div style="display: flex; justify-content: space-between;">
								<span>处理时间:</span>
								<span style="font-weight: bold;">${Math.round(result.duration / 1000)} 秒</span>
							</div>
						</div>
						<div class="optimization-benefits" style="font-size: 14px; color: #666; line-height: 1.5;">
							<strong>优化效果:</strong><br>
							• 自动添加了分类标签，便于管理和检索<br>
							• 合并了相似问题，提高了内容质量<br>
							• 去除了重复条目，减少了数据冗余<br>
							• 保持了信息完整性和准确性
						</div>
					</div>
					<div class="modal-footer" style="text-align: center; margin-top: 20px;">
						<button type="button" class="btn-primary" onclick="this.closest('.modal-overlay').remove()">确定</button>
					</div>
				</div>
			`;

			document.body.appendChild(dialog);

			// 5秒后自动关闭
			setTimeout(() => {
				if (dialog.parentNode) {
					dialog.parentNode.removeChild(dialog);
				}
			}, 10000);
		};

		// @SERVICE 更新问答难度分布图表
		const updateQADifficultyChart = (qaStats) => {
			const difficultyLabels = ['简单', '一般', '中等', '困难', '复杂'];
			const difficultyData = [];
			const difficultyColors = ['#28a745', '#17a2b8', '#ffc107', '#fd7e14', '#dc3545'];

			for (let i = 1; i <= 5; i++) {
				difficultyData.push(qaStats.byDifficulty[i] || 0);
			}

			const data = [{
				values: difficultyData,
				labels: difficultyLabels,
				type: 'pie',
				marker: {
					colors: difficultyColors
				},
				textinfo: 'label+percent',
				textposition: 'outside'
			}];

			const layout = {
				title: '问答难度分布',
				showlegend: true,
				height: 400,
				margin: { t: 50, b: 50, l: 50, r: 50 }
			};

			try {
				Plotly.newPlot('chart-qa-difficulty', data, layout, {responsive: true});

				// 更新预览表格
				updateQADifficultyPreviewTable(qaStats);
			} catch (error) {
				console.error('问答难度图表更新失败:', error);
			}
		};

		// @SERVICE 更新问答标签分布图表
		const updateQATagsChart = (qaStats) => {
			const tagEntries = Object.entries(qaStats.byTags || {});

			if (tagEntries.length === 0) {
				// 显示空状态
				const emptyData = [{
					x: ['暂无数据'],
					y: [0],
					type: 'bar',
					marker: { color: '#e9ecef' }
				}];

				const layout = {
					title: '问答标签分布',
					xaxis: { title: '标签' },
					yaxis: { title: '数量' },
					height: 400,
					margin: { t: 50, b: 100, l: 50, r: 50 }
				};

				try {
					Plotly.newPlot('chart-qa-tags', emptyData, layout, {responsive: true});
				} catch (error) {
					console.error('问答标签图表更新失败:', error);
				}
				return;
			}

			// 按数量排序，取前10个
			const sortedTags = tagEntries
				.sort((a, b) => b[1] - a[1])
				.slice(0, 10);

			const data = [{
				x: sortedTags.map(([tag]) => tag),
				y: sortedTags.map(([, count]) => count),
				type: 'bar',
				marker: {
					color: '#007bff',
					opacity: 0.8
				}
			}];

			const layout = {
				title: '问答标签分布 (Top 10)',
				xaxis: {
					title: '标签',
					tickangle: -45
				},
				yaxis: { title: '数量' },
				height: 400,
				margin: { t: 50, b: 100, l: 50, r: 50 }
			};

			try {
				Plotly.newPlot('chart-qa-tags', data, layout, {responsive: true});

				// 更新预览表格
				updateQATagsPreviewTable(qaStats);
			} catch (error) {
				console.error('问答标签图表更新失败:', error);
			}
		};

		// @SERVICE 更新问答难度预览表格
		const updateQADifficultyPreviewTable = (qaStats) => {
			const tbody = document.getElementById('qa-difficulty-preview-tbody');
			if (!tbody) return;

			const difficultyLabels = ['简单', '一般', '中等', '困难', '复杂'];
			const total = qaStats.total || 1; // 避免除零

			let html = '';
			for (let i = 1; i <= 5; i++) {
				const count = qaStats.byDifficulty[i] || 0;
				const percentage = ((count / total) * 100).toFixed(1);

				html += `
					<tr>
						<td>${difficultyLabels[i-1]}</td>
						<td>${count}</td>
						<td>${percentage}%</td>
						<td>${qaStats.avgFrequency ? qaStats.avgFrequency.toFixed(1) : '0'}</td>
					</tr>
				`;
			}

			tbody.innerHTML = html || '<tr><td colspan="4" class="no-data">暂无数据</td></tr>';
		};

		// @SERVICE 更新问答标签预览表格
		const updateQATagsPreviewTable = (qaStats) => {
			const tbody = document.getElementById('qa-tags-preview-tbody');
			if (!tbody) return;

			const tagEntries = Object.entries(qaStats.byTags || {});
			const total = qaStats.total || 1; // 避免除零

			if (tagEntries.length === 0) {
				tbody.innerHTML = '<tr><td colspan="4" class="no-data">暂无数据</td></tr>';
				return;
			}

			// 按数量排序，取前5个
			const sortedTags = tagEntries
				.sort((a, b) => b[1] - a[1])
				.slice(0, 5);

			let html = '';
			sortedTags.forEach(([tag, count]) => {
				const percentage = ((count / total) * 100).toFixed(1);

				html += `
					<tr>
						<td>${tag}</td>
						<td>${count}</td>
						<td>${percentage}%</td>
						<td>-</td>
					</tr>
				`;
			});

			tbody.innerHTML = html;
		};

		// ==================== 高并发处理状态UI管理 ====================
		/**
		 * @CLASS 处理状态UI管理器
		 * 负责显示和更新处理状态面板
		 */
		class ProcessingStatusUI {
			constructor() {
				this.panel = null;
				this.isVisible = false;
				this.updateInterval = null;
				this.initializeUI();
			}

			// @SERVICE 初始化UI
			initializeUI() {
				// 创建状态面板HTML
				const panelHTML = `
					<div id="processing-status-panel" class="processing-status-panel">
						<div class="status-panel-header">
							<h3><i class="fas fa-cogs"></i> 处理状态</h3>
							<button id="close-status-panel" class="btn-icon">
								<i class="fas fa-times"></i>
							</button>
						</div>
						<div class="status-panel-body">
							<!-- 总体进度 -->
							<div class="progress-info">
								<div class="progress-bar">
									<div id="overall-progress-fill" class="progress-fill" style="width: 0%"></div>
								</div>
								<div class="progress-text">
									<span id="progress-text">准备中...</span>
									<span id="progress-percentage">0%</span>
								</div>
							</div>

							<!-- AI引擎状态 -->
							<div class="engine-status">
								<div id="kimi-status" class="engine-badge kimi">Kimi: 离线</div>
								<div id="gemini-status" class="engine-badge gemini">Gemini: 离线</div>
							</div>

							<!-- 队列统计 -->
							<div class="queue-stats">
								<div style="display: flex; justify-content: space-between; margin-bottom: var(--spacing-sm);">
									<span>队列状态:</span>
									<span id="queue-status">空闲</span>
								</div>
								<div style="display: flex; justify-content: space-between; margin-bottom: var(--spacing-sm);">
									<span>并发数:</span>
									<span id="concurrent-count">0/10</span>
								</div>
								<div style="display: flex; justify-content: space-between; margin-bottom: var(--spacing-sm);">
									<span>完成/失败:</span>
									<span id="completion-stats">0/0</span>
								</div>
							</div>

							<!-- 文件状态列表 -->
							<div class="file-status-list" id="file-status-list">
								<!-- 动态生成文件状态项 -->
							</div>
						</div>
						<div class="status-panel-actions">
							<button id="pause-processing" class="btn-sm btn-warning">暂停</button>
							<button id="resume-processing" class="btn-sm btn-primary">继续</button>
							<button id="clear-queue" class="btn-sm btn-danger">清空队列</button>
						</div>
					</div>
				`;

				// 添加到页面
				document.body.insertAdjacentHTML('beforeend', panelHTML);
				this.panel = document.getElementById('processing-status-panel');

				// 绑定事件
				this.bindEvents();
			}

			// @EVENT_HANDLER 绑定事件
			bindEvents() {
				// 关闭面板
				document.getElementById('close-status-panel').addEventListener('click', () => {
					this.hide();
				});

				// 暂停处理
				document.getElementById('pause-processing').addEventListener('click', () => {
					if (queueManager) {
						queueManager.pauseProcessing();
					}
				});

				// 继续处理
				document.getElementById('resume-processing').addEventListener('click', () => {
					if (queueManager) {
						queueManager.resumeProcessing();
					}
				});

				// 清空队列
				document.getElementById('clear-queue').addEventListener('click', () => {
					if (confirm('确定要清空处理队列吗？这将停止所有正在进行的处理。')) {
						if (queueManager) {
							queueManager.clearQueue();
						}
						this.hide();
					}
				});
			}

			// @SERVICE 显示面板
			show() {
				if (this.panel) {
					this.panel.classList.add('active');
					this.isVisible = true;
					this.startUpdating();
				}
			}

			// @SERVICE 隐藏面板
			hide() {
				if (this.panel) {
					this.panel.classList.remove('active');
					this.isVisible = false;
					this.stopUpdating();
				}
			}

			// @SERVICE 开始定时更新
			startUpdating() {
				if (this.updateInterval) {
					clearInterval(this.updateInterval);
				}

				this.updateInterval = setInterval(() => {
					this.updateStatus();
				}, 1000); // 每秒更新一次

				// 立即更新一次
				this.updateStatus();
			}

			// @SERVICE 停止定时更新
			stopUpdating() {
				if (this.updateInterval) {
					clearInterval(this.updateInterval);
					this.updateInterval = null;
				}
			}

			// @SERVICE 更新状态显示
			updateStatus() {
				if (!this.isVisible || !queueManager) return;

				try {
					// 获取队列状态
					const queueStatus = queueManager.getQueueStatus();
					const progressInfo = queueManager.progressManager.getProgressInfo();

					// 更新进度条
					const progressFill = document.getElementById('overall-progress-fill');
					const progressText = document.getElementById('progress-text');
					const progressPercentage = document.getElementById('progress-percentage');

					if (progressFill && progressText && progressPercentage) {
						progressFill.style.width = `${progressInfo.percentage}%`;
						progressText.textContent = queueStatus.isProcessing ?
							`处理中 (${queueStatus.active} 个活跃任务)` : '空闲';
						progressPercentage.textContent = `${progressInfo.percentage}%`;
					}

					// 更新AI引擎状态
					this.updateEngineStatus();

					// 更新队列统计
					const queueStatusEl = document.getElementById('queue-status');
					const concurrentCountEl = document.getElementById('concurrent-count');
					const completionStatsEl = document.getElementById('completion-stats');

					if (queueStatusEl) {
						queueStatusEl.textContent = queueStatus.isProcessing ? '处理中' : '空闲';
					}
					if (concurrentCountEl) {
						concurrentCountEl.textContent = `${queueStatus.active}/${queueStatus.maxConcurrency}`;
					}
					if (completionStatsEl) {
						completionStatsEl.textContent = `${queueStatus.completed}/${queueStatus.failed}`;
					}

					// 更新文件状态列表
					this.updateFileStatusList();

				} catch (error) {
					console.error('更新状态显示失败:', error);
				}
			}

			// @SERVICE 更新AI引擎状态
			updateEngineStatus() {
				if (!apiConfigManager) return;

				const engines = apiConfigManager.getAvailableEngines();
				const kimiStatus = document.getElementById('kimi-status');
				const geminiStatus = document.getElementById('gemini-status');

				// 更新Kimi状态
				if (kimiStatus) {
					const kimiEngine = engines.find(e => e.name === 'kimi');
					if (kimiEngine) {
						kimiStatus.textContent = 'Kimi: 在线';
						kimiStatus.className = 'engine-badge kimi';
					} else {
						kimiStatus.textContent = 'Kimi: 离线';
						kimiStatus.className = 'engine-badge offline';
					}
				}

				// 更新Gemini状态
				if (geminiStatus) {
					const geminiEngine = engines.find(e => e.name === 'gemini');
					if (geminiEngine) {
						geminiStatus.textContent = 'Gemini: 在线';
						geminiStatus.className = 'engine-badge gemini';
					} else {
						geminiStatus.textContent = 'Gemini: 离线';
						geminiStatus.className = 'engine-badge offline';
					}
				}
			}

			// @SERVICE 更新文件状态列表
			updateFileStatusList() {
				if (!fileRegistry) return;

				const fileStatusList = document.getElementById('file-status-list');
				if (!fileStatusList) return;

				const files = fileRegistry.getAllFiles();
				const recentFiles = files.slice(-10); // 只显示最近的10个文件

				const html = recentFiles.map(file => `
					<div class="file-status-item">
						<div class="file-name" title="${file.name}">${file.name}</div>
						<div class="file-status-badge ${file.status}">${this.getStatusText(file.status)}</div>
					</div>
				`).join('');

				fileStatusList.innerHTML = html || '<div style="text-align: center; color: var(--gray-500);">暂无文件</div>';
			}

			// @UTIL 获取状态文本
			getStatusText(status) {
				const statusMap = {
					[FILE_STATUS.PENDING]: '等待',
					[FILE_STATUS.PROCESSING]: '处理中',
					[FILE_STATUS.COMPLETED]: '完成',
					[FILE_STATUS.FAILED]: '失败',
					[FILE_STATUS.SKIPPED]: '跳过'
				};
				return statusMap[status] || status;
			}
		}

		// @GLOBAL 状态UI管理器实例
		let processingStatusUI = null;

		// @SERVICE 初始化状态UI
		const initializeStatusUI = () => {
			if (!processingStatusUI) {
				processingStatusUI = new ProcessingStatusUI();
			}
		};

		// @SERVICE 显示处理状态
		const showProcessingStatus = () => {
			if (!processingStatusUI) {
				initializeStatusUI();
			}
			processingStatusUI.show();
		};

		// @SERVICE 隐藏处理状态
		const hideProcessingStatus = () => {
			if (processingStatusUI) {
				processingStatusUI.hide();
			}
		};

		// ==================== QA OPTIMIZATION SYSTEM ====================
		/**
		 * 问答题集优化系统
		 * @SERVICE 问答题集标签化和去重优化功能
		 */

		// @SHARED_CONSTANT 标签分类体系
		const TAG_CATEGORIES = {
			primary: ['订单问题', '收入问题', '车辆问题', '技术问题', '注册问题', '平台使用', '政策规则', '其他问题'],
			secondary: ['紧急', '常见', '复杂', '简单', '特殊情况'],
			difficulty: [1, 2, 3, 4, 5], // 1=简单, 5=复杂
			frequency: ['高频', '中频', '低频'],
			urgency: ['立即处理', '优先处理', '正常处理', '延后处理']
		};

		// @SHARED_CONSTANT 关键词映射表
		const KEYWORD_TAG_MAPPING = {
			'订单': ['订单问题', '常见'],
			'取消': ['订单问题', '紧急'],
			'收入': ['收入问题', '常见'],
			'提现': ['收入问题', '高频'],
			'车辆': ['车辆问题', '中频'],
			'审核': ['车辆问题', '注册问题'],
			'注册': ['注册问题', '常见'],
			'登录': ['技术问题', '常见'],
			'APP': ['技术问题', '平台使用'],
			'系统': ['技术问题', '复杂'],
			'客服': ['平台使用', '常见'],
			'投诉': ['其他问题', '紧急'],
			'违规': ['政策规则', '紧急']
		};

		/**
		 * @CLASS 问答标签分析器
		 * 负责为问答条目自动添加分类标签
		 */
		class QATagAnalyzer {
			constructor() {
				this.tagCategories = TAG_CATEGORIES; // 标签分类体系
				this.keywordMapping = KEYWORD_TAG_MAPPING; // 关键词映射表
			}

			// @SERVICE 分析问答条目并生成标签
			async analyzeAndTag(qaItem) {
				try {
					const tags = {
						primary: [],
						secondary: [],
						difficulty: 2, // 默认难度
						frequency: '中频',
						urgency: '正常处理',
						auto: true, // 标记为自动生成
						confidence: 0 // 置信度
					};

					// 基于关键词的基础标签分析
					const basicTags = this.analyzeByKeywords(qaItem);
					tags.primary = basicTags.primary;
					tags.secondary = basicTags.secondary;

					// 使用AI进行深度分析（如果可用）
					if (dualLLMManager && dualLLMManager.isAvailable()) {
						const aiTags = await this.analyzeByAI(qaItem);
						if (aiTags) {
							// 合并AI分析结果
							tags.primary = [...new Set([...tags.primary, ...aiTags.primary])];
							tags.secondary = [...new Set([...tags.secondary, ...aiTags.secondary])];
							tags.difficulty = aiTags.difficulty || tags.difficulty;
							tags.frequency = aiTags.frequency || tags.frequency;
							tags.urgency = aiTags.urgency || tags.urgency;
							tags.confidence = aiTags.confidence || 0.7;
						}
					}

					// 如果没有主要标签，设置默认标签
					if (tags.primary.length === 0) {
						tags.primary = ['其他问题'];
					}

					// 如果没有次要标签，设置默认标签
					if (tags.secondary.length === 0) {
						tags.secondary = ['常见'];
					}

					return tags;

				} catch (error) {
					console.error('标签分析失败:', error);
					return this.getDefaultTags();
				}
			}

			// @SERVICE 基于关键词的标签分析
			analyzeByKeywords(qaItem) {
				const text = `${qaItem.question} ${qaItem.answer}`.toLowerCase();
				const foundTags = {
					primary: [],
					secondary: []
				};

				// 遍历关键词映射表
				Object.entries(this.keywordMapping).forEach(([keyword, tags]) => {
					if (text.includes(keyword.toLowerCase())) {
						tags.forEach(tag => {
							if (this.tagCategories.primary.includes(tag)) {
								if (!foundTags.primary.includes(tag)) {
									foundTags.primary.push(tag);
								}
							} else if (this.tagCategories.secondary.includes(tag)) {
								if (!foundTags.secondary.includes(tag)) {
									foundTags.secondary.push(tag);
								}
							}
						});
					}
				});

				return foundTags;
			}

			// @SERVICE 使用AI进行深度标签分析
			async analyzeByAI(qaItem) {
				try {
					const prompt = `
请分析以下问答内容，并为其分配合适的标签：

问题：${qaItem.question}
答案：${qaItem.answer}

请从以下分类中选择合适的标签：

主要分类：${this.tagCategories.primary.join(', ')}
次要分类：${this.tagCategories.secondary.join(', ')}
难度等级：1-5（1=简单，5=复杂）
频率：高频、中频、低频
紧急程度：立即处理、优先处理、正常处理、延后处理

请以JSON格式返回结果：
{
  "primary": ["主要标签1", "主要标签2"],
  "secondary": ["次要标签1"],
  "difficulty": 数字,
  "frequency": "频率",
  "urgency": "紧急程度",
  "confidence": 0.8
}
					`;

					const response = await dualLLMManager.processRequest(prompt, 'kimi');

					if (response && response.content) {
						const result = this.parseAIResponse(response.content);
						return result;
					}

				} catch (error) {
					console.warn('AI标签分析失败，使用基础分析:', error);
				}

				return null;
			}

			// @UTIL 解析AI响应
			parseAIResponse(content) {
				try {
					// 尝试提取JSON
					const jsonMatch = content.match(/\{[\s\S]*\}/);
					if (jsonMatch) {
						const result = JSON.parse(jsonMatch[0]);

						// 验证结果格式
						if (result.primary && Array.isArray(result.primary)) {
							return {
								primary: result.primary.filter(tag => this.tagCategories.primary.includes(tag)),
								secondary: result.secondary ? result.secondary.filter(tag => this.tagCategories.secondary.includes(tag)) : [],
								difficulty: Math.max(1, Math.min(5, result.difficulty || 2)),
								frequency: this.tagCategories.frequency.includes(result.frequency) ? result.frequency : '中频',
								urgency: this.tagCategories.urgency.includes(result.urgency) ? result.urgency : '正常处理',
								confidence: Math.max(0, Math.min(1, result.confidence || 0.7))
							};
						}
					}
				} catch (error) {
					console.warn('AI响应解析失败:', error);
				}

				return null;
			}

			// @UTIL 获取默认标签
			getDefaultTags() {
				return {
					primary: ['其他问题'],
					secondary: ['常见'],
					difficulty: 2,
					frequency: '中频',
					urgency: '正常处理',
					auto: true,
					confidence: 0.3
				};
			}

			// @SERVICE 批量标签分析
			async batchAnalyze(qaItems, progressCallback) {
				const results = [];
				const total = qaItems.length;

				for (let i = 0; i < qaItems.length; i++) {
					const item = qaItems[i];

					// 分析标签
					const tags = await this.analyzeAndTag(item);

					// 添加标签到问答条目
					const taggedItem = {
						...item,
						tags: tags,
						taggedAt: Date.now()
					};

					results.push(taggedItem);

					// 报告进度
					if (progressCallback) {
						progressCallback({
							current: i + 1,
							total: total,
							percentage: Math.round(((i + 1) / total) * 100),
							item: taggedItem
						});
					}

					// 避免API调用过于频繁
					if (i < qaItems.length - 1) {
						await new Promise(resolve => setTimeout(resolve, 100));
					}
				}

				return results;
			}
		}

		/**
		 * @CLASS 问答去重优化管理器
		 * 负责批量去重和质量优化
		 */
		class QADeduplicationManager {
			constructor() {
				this.batchSize = 100; // 批处理大小
				this.similarityThreshold = 0.85; // 相似度阈值
				this.processedCount = 0; // 已处理数量
				this.duplicateGroups = []; // 重复组
				this.qualityScores = new Map(); // 质量评分缓存
			}

			// @SERVICE 处理批量去重
			async processBatch(qaItems, progressCallback) {
				try {
					console.log(`[去重优化] 开始处理 ${qaItems.length} 个问答条目`);

					// 第一步：相似度分析和分组
					const similarGroups = await this.findSimilarItems(qaItems, progressCallback);

					// 第二步：质量评估
					const qualityScores = await this.evaluateQuality(qaItems, progressCallback);

					// 第三步：合并和去重
					const optimizedItems = await this.mergeAndDeduplicate(similarGroups, qualityScores, progressCallback);

					// 第四步：最终验证
					const finalItems = await this.finalValidation(optimizedItems, progressCallback);

					console.log(`[去重优化] 完成处理，从 ${qaItems.length} 条优化为 ${finalItems.length} 条`);

					return {
						success: true,
						original: qaItems.length,
						optimized: finalItems.length,
						reduction: qaItems.length - finalItems.length,
						reductionRate: Math.round(((qaItems.length - finalItems.length) / qaItems.length) * 100),
						items: finalItems,
						duplicateGroups: similarGroups,
						qualityScores: qualityScores
					};

				} catch (error) {
					console.error('[去重优化] 处理失败:', error);
					return {
						success: false,
						error: error.message,
						items: qaItems // 返回原始数据
					};
				}
			}

			// @SERVICE 查找相似条目
			async findSimilarItems(qaItems, progressCallback) {
				const similarGroups = [];
				const processed = new Set();

				for (let i = 0; i < qaItems.length; i++) {
					if (processed.has(i)) continue;

					const currentItem = qaItems[i];
					const similarItems = [{ index: i, item: currentItem, similarity: 1.0 }];

					// 与后续条目比较
					for (let j = i + 1; j < qaItems.length; j++) {
						if (processed.has(j)) continue;

						const compareItem = qaItems[j];
						const similarity = await this.calculateSimilarity(currentItem, compareItem);

						if (similarity >= this.similarityThreshold) {
							similarItems.push({ index: j, item: compareItem, similarity: similarity });
							processed.add(j);
						}
					}

					// 如果找到相似条目，创建组
					if (similarItems.length > 1) {
						similarGroups.push({
							id: `group_${similarGroups.length + 1}`,
							items: similarItems,
							avgSimilarity: similarItems.reduce((sum, item) => sum + item.similarity, 0) / similarItems.length
						});
					}

					processed.add(i);

					// 报告进度
					if (progressCallback) {
						progressCallback({
							step: '相似度分析',
							current: i + 1,
							total: qaItems.length,
							percentage: Math.round(((i + 1) / qaItems.length) * 25) // 占总进度25%
						});
					}
				}

				console.log(`[去重优化] 发现 ${similarGroups.length} 个相似组`);
				return similarGroups;
			}

			// @SERVICE 计算两个问答条目的相似度
			async calculateSimilarity(item1, item2) {
				try {
					// 基础文本相似度计算
					const questionSim = this.textSimilarity(item1.question, item2.question);
					const answerSim = this.textSimilarity(item1.answer, item2.answer);

					// 综合相似度（问题权重70%，答案权重30%）
					const basicSimilarity = questionSim * 0.7 + answerSim * 0.3;

					// 如果基础相似度较高，使用AI进行深度分析
					if (basicSimilarity > 0.6 && dualLLMManager && dualLLMManager.isAvailable()) {
						const aiSimilarity = await this.calculateAISimilarity(item1, item2);
						if (aiSimilarity !== null) {
							// AI结果权重60%，基础计算权重40%
							return aiSimilarity * 0.6 + basicSimilarity * 0.4;
						}
					}

					return basicSimilarity;

				} catch (error) {
					console.warn('相似度计算失败，使用基础算法:', error);
					return this.textSimilarity(item1.question, item2.question) * 0.7 +
						   this.textSimilarity(item1.answer, item2.answer) * 0.3;
				}
			}

			// @UTIL 基础文本相似度计算
			textSimilarity(text1, text2) {
				if (!text1 || !text2) return 0;

				// 转换为小写并分词
				const words1 = text1.toLowerCase().split(/\s+/);
				const words2 = text2.toLowerCase().split(/\s+/);

				// 计算交集
				const intersection = words1.filter(word => words2.includes(word));
				const union = [...new Set([...words1, ...words2])];

				// Jaccard相似度
				return intersection.length / union.length;
			}

			// @SERVICE 使用AI计算相似度
			async calculateAISimilarity(item1, item2) {
				try {
					const prompt = `
请分析以下两个问答条目的相似度，返回0-1之间的数值（1表示完全相同，0表示完全不同）：

问答1：
问题：${item1.question}
答案：${item1.answer}

问答2：
问题：${item2.question}
答案：${item2.answer}

请考虑以下因素：
1. 问题的语义相似度
2. 答案的内容重叠度
3. 解决的问题是否相同
4. 信息的完整性差异

请只返回一个0-1之间的数字，例如：0.85
					`;

					const response = await dualLLMManager.processRequest(prompt, 'gemini');

					if (response && response.content) {
						const similarity = parseFloat(response.content.trim());
						if (!isNaN(similarity) && similarity >= 0 && similarity <= 1) {
							return similarity;
						}
					}

				} catch (error) {
					console.warn('AI相似度分析失败:', error);
				}

				return null;
			}

			// @SERVICE 质量评估
			async evaluateQuality(qaItems, progressCallback) {
				const qualityScores = new Map();

				for (let i = 0; i < qaItems.length; i++) {
					const item = qaItems[i];
					const score = await this.calculateQualityScore(item);
					qualityScores.set(i, score);

					// 报告进度
					if (progressCallback) {
						progressCallback({
							step: '质量评估',
							current: i + 1,
							total: qaItems.length,
							percentage: 25 + Math.round(((i + 1) / qaItems.length) * 25) // 占总进度25-50%
						});
					}
				}

				return qualityScores;
			}

			// @SERVICE 计算质量评分
			async calculateQualityScore(item) {
				let score = 0;

				// 基础评分标准
				// 1. 问题长度和清晰度 (20分)
				const questionLength = item.question.length;
				if (questionLength > 10 && questionLength < 200) {
					score += 20;
				} else if (questionLength >= 5) {
					score += 10;
				}

				// 2. 答案完整性和详细程度 (30分)
				const answerLength = item.answer.length;
				if (answerLength > 50) {
					score += 30;
				} else if (answerLength > 20) {
					score += 20;
				} else if (answerLength > 10) {
					score += 10;
				}

				// 3. 包含操作步骤 (20分)
				if (item.operationSteps && item.operationSteps.length > 0) {
					score += 20;
				}

				// 4. 适用场景描述 (15分)
				if (item.applicableScenarios && item.applicableScenarios.length > 10) {
					score += 15;
				}

				// 5. 标签完整性 (15分)
				if (item.tags && item.tags.primary && item.tags.primary.length > 0) {
					score += 15;
				}

				// 使用AI进行深度质量评估（如果可用）
				if (dualLLMManager && dualLLMManager.isAvailable()) {
					const aiScore = await this.calculateAIQualityScore(item);
					if (aiScore !== null) {
						// AI评分权重60%，基础评分权重40%
						score = aiScore * 0.6 + score * 0.4;
					}
				}

				return Math.min(100, Math.max(0, score));
			}

			// @SERVICE 使用AI计算质量评分
			async calculateAIQualityScore(item) {
				try {
					const prompt = `
请评估以下问答条目的质量，返回0-100的评分：

问题：${item.question}
答案：${item.answer}
操作步骤：${item.operationSteps || '无'}
适用场景：${item.applicableScenarios || '无'}

评分标准：
- 问题清晰度和具体性 (25分)
- 答案完整性和准确性 (35分)
- 实用性和可操作性 (25分)
- 信息组织和表达质量 (15分)

请只返回一个0-100之间的数字，例如：85
					`;

					const response = await dualLLMManager.processRequest(prompt, 'kimi');

					if (response && response.content) {
						const score = parseFloat(response.content.trim());
						if (!isNaN(score) && score >= 0 && score <= 100) {
							return score;
						}
					}

				} catch (error) {
					console.warn('AI质量评估失败:', error);
				}

				return null;
			}

			// @SERVICE 合并和去重
			async mergeAndDeduplicate(similarGroups, qualityScores, progressCallback) {
				const optimizedItems = [];
				const processedIndices = new Set();

				// 处理相似组
				for (let i = 0; i < similarGroups.length; i++) {
					const group = similarGroups[i];

					// 选择质量最高的条目作为基础
					const bestItem = this.selectBestItem(group.items, qualityScores);

					// 合并其他条目的信息
					const mergedItem = await this.mergeItems(group.items, bestItem, qualityScores);

					optimizedItems.push(mergedItem);

					// 标记已处理的索引
					group.items.forEach(item => processedIndices.add(item.index));

					// 报告进度
					if (progressCallback) {
						progressCallback({
							step: '合并去重',
							current: i + 1,
							total: similarGroups.length,
							percentage: 50 + Math.round(((i + 1) / similarGroups.length) * 25) // 占总进度50-75%
						});
					}
				}

				// 添加未处理的独立条目
				for (let i = 0; i < qualityScores.size; i++) {
					if (!processedIndices.has(i)) {
						// 这里需要从原始数据中获取条目
						// 暂时跳过，在实际实现中需要传入原始数据引用
					}
				}

				return optimizedItems;
			}

			// @UTIL 选择最佳条目
			selectBestItem(items, qualityScores) {
				let bestItem = items[0];
				let bestScore = qualityScores.get(bestItem.index) || 0;

				for (const item of items) {
					const score = qualityScores.get(item.index) || 0;
					if (score > bestScore) {
						bestScore = score;
						bestItem = item;
					}
				}

				return bestItem;
			}

			// @SERVICE 合并条目信息
			async mergeItems(items, bestItem, qualityScores) {
				// 基础合并逻辑
				const merged = { ...bestItem.item };

				// 合并操作步骤
				const allSteps = [];
				items.forEach(item => {
					if (item.item.operationSteps) {
						allSteps.push(...item.item.operationSteps.split(';'));
					}
				});
				if (allSteps.length > 0) {
					merged.operationSteps = [...new Set(allSteps)].join(';');
				}

				// 合并适用场景
				const allScenarios = [];
				items.forEach(item => {
					if (item.item.applicableScenarios) {
						allScenarios.push(item.item.applicableScenarios);
					}
				});
				if (allScenarios.length > 0) {
					merged.applicableScenarios = [...new Set(allScenarios)].join('；');
				}

				// 合并标签
				if (items.length > 1) {
					const allTags = { primary: [], secondary: [] };
					items.forEach(item => {
						if (item.item.tags) {
							if (item.item.tags.primary) allTags.primary.push(...item.item.tags.primary);
							if (item.item.tags.secondary) allTags.secondary.push(...item.item.tags.secondary);
						}
					});

					merged.tags = {
						...merged.tags,
						primary: [...new Set(allTags.primary)],
						secondary: [...new Set(allTags.secondary)]
					};
				}

				// 添加合并信息
				merged.mergedFrom = items.map(item => item.index);
				merged.mergedAt = Date.now();
				merged.qualityScore = qualityScores.get(bestItem.index);

				return merged;
			}

			// @SERVICE 最终验证
			async finalValidation(items, progressCallback) {
				// 最终质量检查和验证
				const validatedItems = [];

				for (let i = 0; i < items.length; i++) {
					const item = items[i];

					// 基本验证
					if (item.question && item.question.trim().length > 0 &&
						item.answer && item.answer.trim().length > 0) {
						validatedItems.push(item);
					}

					// 报告进度
					if (progressCallback) {
						progressCallback({
							step: '最终验证',
							current: i + 1,
							total: items.length,
							percentage: 75 + Math.round(((i + 1) / items.length) * 25) // 占总进度75-100%
						});
					}
				}

				return validatedItems;
			}
		}

		/**
		 * @CLASS 问答优化管理器
		 * 统一管理标签化和去重优化流程
		 */
		class QAOptimizationManager {
			constructor() {
				this.tagAnalyzer = new QATagAnalyzer(); // 标签分析器
				this.deduplicationManager = new QADeduplicationManager(); // 去重管理器
				this.batchSize = 100; // 批处理大小
				this.isProcessing = false; // 处理状态
				this.processedCount = 0; // 已处理数量
				this.optimizationHistory = []; // 优化历史记录
			}

			// @SERVICE 检查是否需要优化
			shouldOptimize(qaDatasetLength) {
				return qaDatasetLength >= this.batchSize;
			}

			// @SERVICE 执行完整优化流程
			async optimizeQADataset(qaItems, options = {}) {
				if (this.isProcessing) {
					throw new Error('优化流程正在进行中，请稍后再试');
				}

				this.isProcessing = true;
				const startTime = Date.now();

				try {
					console.log(`[问答优化] 开始优化 ${qaItems.length} 个问答条目`);

					const result = {
						success: false,
						startTime: startTime,
						endTime: null,
						duration: 0,
						original: {
							count: qaItems.length,
							items: qaItems
						},
						optimized: {
							count: 0,
							items: []
						},
						steps: {
							tagging: null,
							deduplication: null
						},
						statistics: {
							tagged: 0,
							duplicatesRemoved: 0,
							qualityImproved: 0,
							reductionRate: 0
						}
					};

					// 第一阶段：标签化处理
					console.log('[问答优化] 第一阶段：标签化处理');
					const taggingResult = await this.performTagging(qaItems, options.progressCallback);
					result.steps.tagging = taggingResult;
					result.statistics.tagged = taggingResult.success ? taggingResult.items.length : 0;

					if (!taggingResult.success) {
						throw new Error('标签化处理失败: ' + taggingResult.error);
					}

					// 第二阶段：去重优化
					console.log('[问答优化] 第二阶段：去重优化');
					const deduplicationResult = await this.performDeduplication(taggingResult.items, options.progressCallback);
					result.steps.deduplication = deduplicationResult;

					if (deduplicationResult.success) {
						result.optimized.count = deduplicationResult.items.length;
						result.optimized.items = deduplicationResult.items;
						result.statistics.duplicatesRemoved = deduplicationResult.reduction;
						result.statistics.reductionRate = deduplicationResult.reductionRate;
					}

					// 计算最终结果
					result.endTime = Date.now();
					result.duration = result.endTime - result.startTime;
					result.success = taggingResult.success && deduplicationResult.success;

					// 保存优化历史
					this.saveOptimizationHistory(result);

					console.log(`[问答优化] 优化完成，用时 ${result.duration}ms`);
					console.log(`[问答优化] 原始数量: ${result.original.count}, 优化后: ${result.optimized.count}, 减少: ${result.statistics.duplicatesRemoved} (${result.statistics.reductionRate}%)`);

					return result;

				} catch (error) {
					console.error('[问答优化] 优化失败:', error);
					return {
						success: false,
						error: error.message,
						startTime: startTime,
						endTime: Date.now(),
						duration: Date.now() - startTime,
						original: { count: qaItems.length, items: qaItems },
						optimized: { count: 0, items: [] }
					};
				} finally {
					this.isProcessing = false;
				}
			}

			// @SERVICE 执行标签化处理
			async performTagging(qaItems, progressCallback) {
				try {
					const taggedItems = await this.tagAnalyzer.batchAnalyze(qaItems, (progress) => {
						if (progressCallback) {
							progressCallback({
								stage: 'tagging',
								step: '标签分析',
								current: progress.current,
								total: progress.total,
								percentage: Math.round(progress.percentage / 2), // 标签化占总进度50%
								item: progress.item
							});
						}
					});

					return {
						success: true,
						items: taggedItems,
						count: taggedItems.length,
						duration: 0 // 在实际实现中计算
					};

				} catch (error) {
					console.error('[标签化] 处理失败:', error);
					return {
						success: false,
						error: error.message,
						items: qaItems // 返回原始数据
					};
				}
			}

			// @SERVICE 执行去重优化
			async performDeduplication(qaItems, progressCallback) {
				try {
					const result = await this.deduplicationManager.processBatch(qaItems, (progress) => {
						if (progressCallback) {
							progressCallback({
								stage: 'deduplication',
								step: progress.step,
								current: progress.current,
								total: progress.total,
								percentage: 50 + Math.round(progress.percentage / 2), // 去重占总进度50%
								details: progress
							});
						}
					});

					return result;

				} catch (error) {
					console.error('[去重优化] 处理失败:', error);
					return {
						success: false,
						error: error.message,
						items: qaItems // 返回原始数据
					};
				}
			}

			// @SERVICE 保存优化历史
			saveOptimizationHistory(result) {
				const historyItem = {
					id: `opt_${Date.now()}`,
					timestamp: result.startTime,
					duration: result.duration,
					originalCount: result.original.count,
					optimizedCount: result.optimized.count,
					reductionRate: result.statistics.reductionRate,
					success: result.success,
					error: result.error || null
				};

				this.optimizationHistory.push(historyItem);

				// 只保留最近20条记录
				if (this.optimizationHistory.length > 20) {
					this.optimizationHistory = this.optimizationHistory.slice(-20);
				}

				// 保存到本地存储
				try {
					localStorage.setItem('qa_optimization_history', JSON.stringify(this.optimizationHistory));
				} catch (error) {
					console.warn('保存优化历史失败:', error);
				}
			}

			// @SERVICE 获取优化历史
			getOptimizationHistory() {
				return this.optimizationHistory;
			}

			// @SERVICE 获取优化统计
			getOptimizationStats() {
				const history = this.optimizationHistory.filter(item => item.success);

				if (history.length === 0) {
					return {
						totalOptimizations: 0,
						totalItemsProcessed: 0,
						totalItemsRemoved: 0,
						averageReductionRate: 0,
						averageDuration: 0
					};
				}

				const totalProcessed = history.reduce((sum, item) => sum + item.originalCount, 0);
				const totalRemoved = history.reduce((sum, item) => sum + (item.originalCount - item.optimizedCount), 0);
				const totalDuration = history.reduce((sum, item) => sum + item.duration, 0);

				return {
					totalOptimizations: history.length,
					totalItemsProcessed: totalProcessed,
					totalItemsRemoved: totalRemoved,
					averageReductionRate: Math.round(history.reduce((sum, item) => sum + item.reductionRate, 0) / history.length),
					averageDuration: Math.round(totalDuration / history.length)
				};
			}

			// @SERVICE 清理优化历史
			clearOptimizationHistory() {
				this.optimizationHistory = [];
				try {
					localStorage.removeItem('qa_optimization_history');
				} catch (error) {
					console.warn('清理优化历史失败:', error);
				}
			}

			// @SERVICE 加载优化历史
			loadOptimizationHistory() {
				try {
					const saved = localStorage.getItem('qa_optimization_history');
					if (saved) {
						this.optimizationHistory = JSON.parse(saved);
					}
				} catch (error) {
					console.warn('加载优化历史失败:', error);
					this.optimizationHistory = [];
				}
			}
		}

		// @GLOBAL 问答优化管理器实例
		let qaOptimizationManager = null;

		// @SERVICE 初始化问答优化管理器
		const initializeQAOptimization = () => {
			if (!qaOptimizationManager) {
				qaOptimizationManager = new QAOptimizationManager();
				qaOptimizationManager.loadOptimizationHistory();
				console.log('✓ 问答优化管理器初始化完成');
			}
		};

		// ==================== REPORT GENERATION FUNCTIONS ====================
		/**
		 * 报告生成功能
		 * @SERVICE 报告生成功能
		 */

		// @EVENT_HANDLER 报告功能事件设置
		const setupReportEvents = () => {
			// 生成报告按钮
			const generateBtn = document.getElementById('generate-report-btn');
			if (generateBtn) {
				generateBtn.addEventListener('click', startReportGeneration);
			}

			// 文件选择按钮
			const selectFilesBtn = document.getElementById('select-files-btn');
			if (selectFilesBtn) {
				selectFilesBtn.addEventListener('click', showFileSelectionDialog);
			}

			// 取消生成按钮
			const cancelBtn = document.getElementById('cancel-report-btn');
			if (cancelBtn) {
				cancelBtn.addEventListener('click', cancelReportGeneration);
			}

			// 导出报告按钮
			const exportBtn = document.getElementById('export-report-btn');
			if (exportBtn) {
				exportBtn.addEventListener('click', exportCurrentReport);
			}

			// 监听报告生成进度更新
			window.addEventListener('reportProgressUpdate', handleProgressUpdate);

			// 初始化日期范围
			initializeDateRange();

			// 加载报告历史
			loadReportHistory();
		};

		// @SERVICE 显示文件选择对话框
		const showFileSelectionDialog = () => {
			if (!fileRegistry) {
				alert('文件注册表未初始化');
				return;
			}

			const files = fileRegistry.getAllFiles().filter(f => f.status === 'completed');
			if (files.length === 0) {
				alert('没有已完成分析的文件');
				return;
			}

			// 创建文件选择对话框
			const dialog = document.createElement('div');
			dialog.className = 'modal-overlay';
			dialog.style.cssText = `
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(0,0,0,0.5);
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 1000;
			`;

			dialog.innerHTML = `
				<div class="modal-dialog" style="background: white; border-radius: 8px; padding: 20px; max-width: 600px; width: 90%; max-height: 80%; overflow-y: auto;">
					<div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #ddd; padding-bottom: 15px;">
						<h3 style="margin: 0;">选择要包含在报告中的文件</h3>
						<button type="button" class="modal-close" onclick="this.closest('.modal-overlay').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
					</div>
					<div class="modal-body">
						<div class="file-selection-list" style="max-height: 300px; overflow-y: auto;">
							${files.map(file => `
								<label class="file-selection-item" style="display: flex; align-items: center; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 8px; cursor: pointer;">
									<input type="checkbox" value="${file.id}" checked style="margin-right: 10px;">
									<div style="flex: 1;">
										<div class="file-name" style="font-weight: 600;">${file.name}</div>
										<div class="file-meta" style="font-size: 0.85rem; color: #666;">${new Date(file.completedAt || Date.now()).toLocaleDateString()}</div>
									</div>
								</label>
							`).join('')}
						</div>
					</div>
					<div class="modal-footer" style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px; padding-top: 15px; border-top: 1px solid #ddd;">
						<button type="button" class="btn-sm btn-outline-secondary" onclick="this.closest('.modal-overlay').remove()" style="padding: 8px 16px; border: 1px solid #ccc; background: white; border-radius: 4px; cursor: pointer;">取消</button>
						<button type="button" class="btn-sm btn-primary" onclick="confirmFileSelection()" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">确认选择</button>
					</div>
				</div>
			`;

			document.body.appendChild(dialog);
		};

		// @SERVICE 确认文件选择
		window.confirmFileSelection = () => {
			const checkboxes = document.querySelectorAll('.file-selection-list input[type="checkbox"]:checked');
			const selectedFiles = Array.from(checkboxes).map(cb => cb.value);

			// 更新选择状态
			updateSelectedFiles(selectedFiles);

			// 关闭对话框
			document.querySelector('.modal-overlay').remove();
		};

		// @SERVICE 更新选择的文件
		const updateSelectedFiles = (fileIds) => {
			const countElement = document.getElementById('selected-files-count');
			if (countElement) {
				countElement.textContent = fileIds.length;
			}

			// 存储选择的文件ID
			window.selectedReportFiles = fileIds;
		};

		// @SERVICE 更新文件选择状态
		const updateFileSelectionStatus = () => {
			if (!fileRegistry) return;

			const completedFiles = fileRegistry.getAllFiles().filter(f => f.status === 'completed');
			const countElement = document.getElementById('selected-files-count');

			if (countElement) {
				const selectedCount = window.selectedReportFiles ? window.selectedReportFiles.length : completedFiles.length;
				countElement.textContent = selectedCount;
			}

			// 如果没有选择，默认选择所有已完成的文件
			if (!window.selectedReportFiles) {
				window.selectedReportFiles = completedFiles.map(f => f.id);
			}
		};

		// @SERVICE 初始化日期范围
		const initializeDateRange = () => {
			const endDate = document.getElementById('report-end-date');
			const startDate = document.getElementById('report-start-date');

			if (endDate) {
				endDate.value = new Date().toISOString().split('T')[0];
			}

			if (startDate) {
				const thirtyDaysAgo = new Date();
				thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
				startDate.value = thirtyDaysAgo.toISOString().split('T')[0];
			}
		};

		// @SERVICE 开始生成报告
		const startReportGeneration = async () => {
			if (!reportGenerator) {
				alert('报告生成器未初始化');
				return;
			}

			// 检查是否有已分析的数据
			if (!appDataManager || !appDataManager.data.metrics.length) {
				alert('请先分析一些对话文件，然后再生成报告');
				return;
			}

			try {
				// 更新文件选择状态
				updateFileSelectionStatus();

				// 收集配置
				const config = {
					selectedFiles: window.selectedReportFiles || [],
					startDate: document.getElementById('report-start-date')?.value || null,
					endDate: document.getElementById('report-end-date')?.value || null,
					reportType: document.getElementById('report-type')?.value || 'comprehensive',
					exportFormats: getSelectedExportFormats(),
					template: document.getElementById('report-type')?.value || 'comprehensive'
				};

				// 显示进度区域
				document.getElementById('report-config-section').style.display = 'none';
				document.getElementById('report-progress-section').style.display = 'block';
				document.getElementById('report-preview-section').style.display = 'none';

				// 开始生成
				const result = await reportGenerator.generateReport(config);

				if (result.success) {
					// 显示预览
					showReportPreview(result.content);
					// 刷新历史记录
					loadReportHistory();
				}

			} catch (error) {
				console.error('报告生成失败:', error);
				alert(`报告生成失败: ${error.message}`);

				// 重置UI状态
				document.getElementById('report-config-section').style.display = 'block';
				document.getElementById('report-progress-section').style.display = 'none';
			}
		};

		// @SERVICE 获取选择的导出格式
		const getSelectedExportFormats = () => {
			const formats = [];
			if (document.getElementById('export-html')?.checked) formats.push('html');
			if (document.getElementById('export-pdf')?.checked) formats.push('pdf');
			if (document.getElementById('export-word')?.checked) formats.push('word');
			return formats.length > 0 ? formats : ['html'];
		};

		// @SERVICE 处理进度更新
		const handleProgressUpdate = (event) => {
			const { progress, step } = event.detail;

			const progressFill = document.getElementById('report-progress-fill');
			const progressText = document.getElementById('report-progress-text');
			const progressPercentage = document.getElementById('report-progress-percentage');
			const currentStep = document.getElementById('report-current-step');

			if (progressFill) {
				progressFill.style.width = `${progress}%`;
			}

			if (progressText) {
				progressText.textContent = step;
			}

			if (progressPercentage) {
				progressPercentage.textContent = `${progress}%`;
			}

			if (currentStep) {
				currentStep.textContent = step;
			}
		};

		// @SERVICE 取消报告生成
		const cancelReportGeneration = () => {
			if (reportGenerator) {
				reportGenerator.cancelGeneration();
			}

			// 重置UI状态
			document.getElementById('report-config-section').style.display = 'block';
			document.getElementById('report-progress-section').style.display = 'none';
			document.getElementById('report-preview-section').style.display = 'none';
		};

		// @SERVICE 显示报告预览
		const showReportPreview = (reportContent) => {
			document.getElementById('report-progress-section').style.display = 'none';
			document.getElementById('report-preview-section').style.display = 'block';

			const previewContainer = document.getElementById('report-preview-content');
			if (previewContainer) {
				previewContainer.innerHTML = generateReportPreviewHTML(reportContent);
			}

			// 存储当前报告内容
			window.currentReportContent = reportContent;
		};

		// @SERVICE 生成报告预览HTML
		const generateReportPreviewHTML = (reportContent) => {
			const exporter = new ReportExporter();
			return exporter.generateHTMLContent(reportContent);
		};

		// @SERVICE 导出当前报告
		const exportCurrentReport = async () => {
			if (!window.currentReportContent) {
				alert('没有可导出的报告');
				return;
			}

			const formats = getSelectedExportFormats();
			const exporter = new ReportExporter();

			try {
				const results = await exporter.exportReport(window.currentReportContent, formats);

				let successCount = 0;
				let errorMessages = [];

				Object.entries(results).forEach(([format, result]) => {
					if (result.success) {
						successCount++;
					} else {
						errorMessages.push(`${format}: ${result.error}`);
					}
				});

				if (successCount > 0) {
					alert(`成功导出 ${successCount} 个格式的报告`);
				}

				if (errorMessages.length > 0) {
					console.warn('部分格式导出失败:', errorMessages);
				}

			} catch (error) {
				console.error('导出报告失败:', error);
				alert(`导出失败: ${error.message}`);
			}
		};

		// @SERVICE 加载报告历史
		const loadReportHistory = () => {
			if (!reportGenerator) return;

			const history = reportGenerator.getReportHistory();
			const historyContainer = document.getElementById('report-history-list');

			if (!historyContainer) return;

			if (history.length === 0) {
				historyContainer.innerHTML = `
					<div class="empty-state">
						<i class="fas fa-file-alt"></i>
						<p>暂无历史报告</p>
					</div>
				`;
				return;
			}

			historyContainer.innerHTML = history.map(report => `
				<div class="history-item">
					<div class="history-item-info">
						<div class="history-item-title">${report.title}</div>
						<div class="history-item-meta">
							${new Date(report.generatedAt).toLocaleString('zh-CN')} |
							${report.fileCount} 个文件 |
							${report.type}
						</div>
					</div>
					<div class="history-item-actions">
						<button type="button" class="btn-sm btn-outline-primary" onclick="viewHistoryReport('${report.id}')">
							<i class="fas fa-eye"></i> 查看
						</button>
						<button type="button" class="btn-sm btn-outline-danger" onclick="deleteHistoryReport('${report.id}')">
							<i class="fas fa-trash"></i> 删除
						</button>
					</div>
				</div>
			`).join('');
		};

		// @SERVICE 查看历史报告
		window.viewHistoryReport = (reportId) => {
			if (!reportGenerator) return;

			const reportContent = reportGenerator.getReportContent(reportId);
			if (reportContent) {
				showReportPreview(reportContent);
			} else {
				alert('报告内容不存在');
			}
		};

		// @SERVICE 删除历史报告
		window.deleteHistoryReport = (reportId) => {
			if (!reportGenerator) return;

			if (confirm('确定要删除这个报告吗？')) {
				reportGenerator.deleteReport(reportId);
				loadReportHistory();
			}
		};

	</script>
</body>
</html>