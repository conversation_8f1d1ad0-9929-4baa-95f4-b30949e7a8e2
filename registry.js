/**
 * @NAMESPACE QNA_DRIVER_ANALYSIS.CORE.REGISTRY
 * @COMPONENT_ID CENTRAL_REGISTRY_V1
 * @VERSION 1.0.0
 * @CREATED 2025-08-10
 * @AUTHOR jc-yap89
 * @DESCRIPTION 中央组件注册管理系统，统一管理所有组件的注册、依赖关系和生命周期
 * 
 * @DEPENDENCIES
 * - INPUT: 组件元数据、依赖关系定义
 * - OUTPUT: 组件查询接口、依赖图、冲突检测结果
 * - EXTERNAL: 无
 * 
 * @ARCHITECTURE_LAYER CORE
 * @RELATED_MODULES namespace-manager.js, 所有业务模块
 * 
 * @LIFECYCLE_STATUS INITIALIZED
 * @MIGRATION_STATUS NONE
 * 
 * @TAGS @MANAGER @SINGLETON @REGISTRY
 */

// @SHARED_CONSTANT 注册表数据结构
const REGISTRY_KEYS = {
  NAMESPACES: 'namespaces',
  COMPONENTS: 'components', 
  DEPENDENCIES: 'dependencies',
  INTERFACES: 'interfaces',
  IMPLEMENTATIONS: 'implementations',
  LIFECYCLE: 'lifecycle',
  CONFLICTS: 'conflicts',
  VERSIONS: 'versions'
};

// @ENUMERATION 组件状态枚举
export const COMPONENT_STATUS = {
  NOT_IMPLEMENTED: 'NOT_IMPLEMENTED',    // 待实现
  IN_DEVELOPMENT: 'IN_DEVELOPMENT',      // 开发中
  IMPLEMENTED: 'IMPLEMENTED',            // 已实现
  NEEDS_STANDARDIZATION: 'NEEDS_STANDARDIZATION', // 待标准化
  TESTING: 'TESTING',                    // 测试中
  STABLE: 'STABLE',                      // 稳定版本
  DEPRECATED: 'DEPRECATED',              // 已废弃
  MIGRATED: 'MIGRATED'                   // 已迁移
};

// @ENUMERATION 依赖类型枚举
export const DEPENDENCY_TYPE = {
  REQUIRED: 'REQUIRED',        // 必需依赖
  OPTIONAL: 'OPTIONAL',        // 可选依赖
  DEVELOPMENT: 'DEVELOPMENT',  // 开发依赖
  PEER: 'PEER',               // 同级依赖
  EXTERNAL: 'EXTERNAL'        // 外部依赖
};

// @ENUMERATION 冲突类型枚举
export const CONFLICT_TYPE = {
  NAMESPACE_COLLISION: 'NAMESPACE_COLLISION',    // 命名空间冲突
  FUNCTION_DUPLICATION: 'FUNCTION_DUPLICATION', // 功能重复
  CIRCULAR_DEPENDENCY: 'CIRCULAR_DEPENDENCY',   // 循环依赖
  LAYER_VIOLATION: 'LAYER_VIOLATION',           // 层级违规
  VERSION_CONFLICT: 'VERSION_CONFLICT'          // 版本冲突
};

/**
 * @MANAGER 中央注册管理器
 * @SINGLETON 单例模式实现
 * @DESCRIPTION 管理所有组件的注册、查询、依赖关系和冲突检测
 */
export class CentralRegistry {
  constructor() {
    if (CentralRegistry.instance) {
      return CentralRegistry.instance; // 单例模式
    }
    
    // @INITIALIZATION 初始化注册表数据结构
    this.registry = new Map();
    this.registry.set(REGISTRY_KEYS.NAMESPACES, new Map());      // 命名空间注册表
    this.registry.set(REGISTRY_KEYS.COMPONENTS, new Map());      // 组件注册表
    this.registry.set(REGISTRY_KEYS.DEPENDENCIES, new Map());    // 依赖关系图
    this.registry.set(REGISTRY_KEYS.INTERFACES, new Map());      // 接口定义
    this.registry.set(REGISTRY_KEYS.IMPLEMENTATIONS, new Map()); // 接口实现
    this.registry.set(REGISTRY_KEYS.LIFECYCLE, new Map());       // 生命周期状态
    this.registry.set(REGISTRY_KEYS.CONFLICTS, new Set());       // 冲突记录
    this.registry.set(REGISTRY_KEYS.VERSIONS, new Map());        // 版本管理
    
    // @INITIALIZATION 初始化统计信息
    this.stats = {
      totalComponents: 0,
      implementedComponents: 0,
      pendingComponents: 0,
      conflictCount: 0,
      lastUpdate: new Date().toISOString()
    };
    
    CentralRegistry.instance = this;
    console.log('🏗️ 中央注册系统已初始化'); // 中文日志
  }

  /**
   * @SERVICE 注册组件到中央注册表
   * @param {Object} componentMetadata - 组件元数据
   * @param {string} componentMetadata.namespace - 完整命名空间
   * @param {string} componentMetadata.componentId - 唯一组件标识
   * @param {string} componentMetadata.filePath - 文件路径
   * @param {string} componentMetadata.type - 组件类型标签
   * @param {string} componentMetadata.description - 功能描述
   * @param {Array} componentMetadata.dependencies - 依赖列表
   * @param {string} componentMetadata.status - 组件状态
   * @returns {boolean} 注册是否成功
   */
  registerComponent(componentMetadata) {
    try {
      const { namespace, componentId, filePath, type, description, dependencies = [], status = COMPONENT_STATUS.IMPLEMENTED } = componentMetadata;
      
      // @VALIDATION 验证必需字段
      if (!namespace || !componentId || !filePath) {
        throw new Error(`组件注册失败：缺少必需字段 (namespace: ${namespace}, componentId: ${componentId}, filePath: ${filePath})`);
      }
      
      // @CONFLICT_DETECTION 检查命名空间冲突
      if (this.hasNamespaceConflict(namespace, componentId)) {
        this.recordConflict(CONFLICT_TYPE.NAMESPACE_COLLISION, `命名空间冲突: ${namespace}`);
        return false;
      }
      
      // @REGISTRATION 注册命名空间
      const namespaces = this.registry.get(REGISTRY_KEYS.NAMESPACES);
      namespaces.set(namespace, {
        componentId,
        filePath,
        registeredAt: new Date().toISOString()
      });
      
      // @REGISTRATION 注册组件
      const components = this.registry.get(REGISTRY_KEYS.COMPONENTS);
      components.set(componentId, {
        namespace,
        filePath,
        type,
        description,
        dependencies,
        status,
        registeredAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString()
      });
      
      // @REGISTRATION 注册依赖关系
      if (dependencies.length > 0) {
        this.registerDependencies(componentId, dependencies);
      }
      
      // @REGISTRATION 注册生命周期状态
      const lifecycle = this.registry.get(REGISTRY_KEYS.LIFECYCLE);
      lifecycle.set(componentId, {
        status,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      // @STATISTICS 更新统计信息
      this.updateStats();
      
      console.log(`✅ 组件注册成功: ${componentId} (${namespace})`); // 中文日志
      return true;
      
    } catch (error) {
      console.error('❌ 组件注册失败:', error.message); // 中文日志
      return false;
    }
  }

  /**
   * @SERVICE 注册组件依赖关系
   * @param {string} componentId - 组件标识
   * @param {Array} dependencies - 依赖列表
   */
  registerDependencies(componentId, dependencies) {
    const dependencyMap = this.registry.get(REGISTRY_KEYS.DEPENDENCIES);
    
    dependencies.forEach(dep => {
      const { target, type = DEPENDENCY_TYPE.REQUIRED, description = '' } = dep;
      
      if (!dependencyMap.has(componentId)) {
        dependencyMap.set(componentId, []);
      }
      
      dependencyMap.get(componentId).push({
        target,
        type,
        description,
        registeredAt: new Date().toISOString()
      });
      
      // @CONFLICT_DETECTION 检查循环依赖
      if (this.hasCircularDependency(componentId, target)) {
        this.recordConflict(CONFLICT_TYPE.CIRCULAR_DEPENDENCY, `循环依赖: ${componentId} ↔ ${target}`);
      }
    });
  }

  /**
   * @SERVICE 查询组件信息
   * @param {string} componentId - 组件标识
   * @returns {Object|null} 组件信息
   */
  getComponent(componentId) {
    const components = this.registry.get(REGISTRY_KEYS.COMPONENTS);
    return components.get(componentId) || null;
  }

  /**
   * @SERVICE 查询命名空间信息
   * @param {string} namespace - 命名空间
   * @returns {Object|null} 命名空间信息
   */
  getNamespace(namespace) {
    const namespaces = this.registry.get(REGISTRY_KEYS.NAMESPACES);
    return namespaces.get(namespace) || null;
  }

  /**
   * @SERVICE 获取组件依赖图
   * @param {string} componentId - 组件标识
   * @returns {Array} 依赖关系数组
   */
  getDependencyGraph(componentId) {
    const dependencies = this.registry.get(REGISTRY_KEYS.DEPENDENCIES);
    return dependencies.get(componentId) || [];
  }

  /**
   * @UTIL 检查命名空间冲突
   * @param {string} namespace - 命名空间
   * @param {string} componentId - 组件标识
   * @returns {boolean} 是否存在冲突
   */
  hasNamespaceConflict(namespace, componentId) {
    const namespaces = this.registry.get(REGISTRY_KEYS.NAMESPACES);
    const existing = namespaces.get(namespace);
    return existing && existing.componentId !== componentId;
  }

  /**
   * @UTIL 检查循环依赖
   * @param {string} source - 源组件
   * @param {string} target - 目标组件
   * @returns {boolean} 是否存在循环依赖
   */
  hasCircularDependency(source, target) {
    const visited = new Set();
    const recursionStack = new Set();
    
    const dfs = (current) => {
      if (recursionStack.has(current)) return true; // 发现循环
      if (visited.has(current)) return false;
      
      visited.add(current);
      recursionStack.add(current);
      
      const deps = this.getDependencyGraph(current);
      for (const dep of deps) {
        if (dfs(dep.target)) return true;
      }
      
      recursionStack.delete(current);
      return false;
    };
    
    return dfs(source);
  }

  /**
   * @SERVICE 记录冲突
   * @param {string} type - 冲突类型
   * @param {string} description - 冲突描述
   */
  recordConflict(type, description) {
    const conflicts = this.registry.get(REGISTRY_KEYS.CONFLICTS);
    conflicts.add({
      type,
      description,
      timestamp: new Date().toISOString()
    });
    
    console.warn(`⚠️ 检测到冲突: ${type} - ${description}`); // 中文日志
  }

  /**
   * @SERVICE 获取所有冲突
   * @returns {Array} 冲突列表
   */
  getConflicts() {
    const conflicts = this.registry.get(REGISTRY_KEYS.CONFLICTS);
    return Array.from(conflicts);
  }

  /**
   * @SERVICE 获取注册统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return { ...this.stats };
  }

  /**
   * @UTIL 更新统计信息
   */
  updateStats() {
    const components = this.registry.get(REGISTRY_KEYS.COMPONENTS);
    const conflicts = this.registry.get(REGISTRY_KEYS.CONFLICTS);
    
    this.stats.totalComponents = components.size;
    this.stats.implementedComponents = Array.from(components.values())
      .filter(comp => comp.status === COMPONENT_STATUS.IMPLEMENTED || comp.status === COMPONENT_STATUS.STABLE).length;
    this.stats.pendingComponents = this.stats.totalComponents - this.stats.implementedComponents;
    this.stats.conflictCount = conflicts.size;
    this.stats.lastUpdate = new Date().toISOString();
  }

  /**
   * @SERVICE 导出注册表数据
   * @returns {Object} 完整的注册表数据
   */
  exportRegistry() {
    const result = {};
    for (const [key, value] of this.registry.entries()) {
      if (value instanceof Map) {
        result[key] = Object.fromEntries(value);
      } else if (value instanceof Set) {
        result[key] = Array.from(value);
      } else {
        result[key] = value;
      }
    }
    return result;
  }
}

// @SINGLETON 创建全局单例实例
export const centralRegistry = new CentralRegistry();
