# 组件注册表

**注册表版本**: 1.0.0  
**最后更新**: 2025-08-10  
**维护者**: jc-yap89  
**相关性评分**: [RS:5]

## 组件注册概览 [RS:5]

### 注册统计
```
总注册组件: 15个
├── 已实现: 11个 (73%)
├── 待标准化: 11个 (73%)
├── 待实现: 4个 (27%)
└── 已废弃: 0个 (0%)

按层级分布:
├── DATA层: 6个组件
├── PARSING层: 4个组件
├── UI层: 3个组件
├── CORE层: 1个组件 (待实现)
├── REPORTS层: 1个组件 (待实现)
└── UTILS层: 0个组件
```

## 已注册组件清单 [RS:4]

### DATA层组件

#### 1. MAX_CONCURRENCY
```yaml
组件ID: QNA_DRIVER_ANALYSIS.DATA.CONSTANTS.MAX_CONCURRENCY_V1
文件位置: constants.js:7
组件类型: @SHARED_CONSTANT
功能描述: 定义并发处理的最大数量限制
导出类型: named export
依赖关系: 无
使用者: 队列管理、文件处理模块
状态: 已实现，待标准化
注册日期: 2025-08-10
```

#### 2. CSV_MIME
```yaml
组件ID: QNA_DRIVER_ANALYSIS.DATA.CONSTANTS.CSV_MIME_V1
文件位置: constants.js:10
组件类型: @SHARED_CONSTANT
功能描述: CSV文件的MIME类型定义
导出类型: named export
依赖关系: 无
使用者: 文件导出模块
状态: 已实现，待标准化
注册日期: 2025-08-10
```

#### 3. defaultHeaders
```yaml
组件ID: QNA_DRIVER_ANALYSIS.DATA.CONSTANTS.DEFAULT_HEADERS_V1
文件位置: constants.js:13
组件类型: @UTIL
功能描述: 生成默认HTTP请求头的工具函数
导出类型: named export
依赖关系: 无
使用者: LLM API调用模块
状态: 已实现，待标准化
注册日期: 2025-08-10
```

#### 4. STORAGE_KEYS
```yaml
组件ID: QNA_DRIVER_ANALYSIS.DATA.STORAGE.STORAGE_KEYS_V1
文件位置: storage.js:8
组件类型: @SHARED_CONSTANT
功能描述: 本地存储键名的统一定义
导出类型: named export
依赖关系: 无
使用者: 存储管理模块
状态: 已实现，待标准化
注册日期: 2025-08-10
```

#### 5. LEGACY_ENHANCED_KEYS
```yaml
组件ID: QNA_DRIVER_ANALYSIS.DATA.STORAGE.LEGACY_ENHANCED_KEYS_V1
文件位置: storage.js:31
组件类型: @CONFIG_FILE
功能描述: 旧版本存储键名映射，用于数据迁移
导出类型: named export
依赖关系: 无
使用者: 存储管理模块
状态: 已实现，待标准化
注册日期: 2025-08-10
```

#### 6. StorageManager
```yaml
组件ID: QNA_DRIVER_ANALYSIS.DATA.STORAGE.STORAGE_MANAGER_V1
文件位置: storage.js:41
组件类型: @MANAGER
功能描述: 本地存储管理器类，提供统一的存储接口
导出类型: named export (class)
依赖关系: localStorage API
使用者: 应用数据管理模块
状态: 已实现，待标准化
注册日期: 2025-08-10
```

### PARSING层组件

#### 7. parseTimestamp
```yaml
组件ID: QNA_DRIVER_ANALYSIS.PARSING.PARSER.PARSE_TIMESTAMP_V1
文件位置: parser.js:7
组件类型: @UTIL
功能描述: 解析日期时间字符串为时间戳
导出类型: named export
依赖关系: Date API
使用者: 文本解析模块
状态: 已实现，待标准化
注册日期: 2025-08-10
```

#### 8. parseTxtContent
```yaml
组件ID: QNA_DRIVER_ANALYSIS.PARSING.PARSER.PARSE_TXT_CONTENT_V1
文件位置: parser.js:28
组件类型: @SERVICE
功能描述: 解析txt文件内容为标准对话结构
导出类型: named export
依赖关系: parseTimestamp
使用者: 文件处理模块
状态: 已实现，待标准化
注册日期: 2025-08-10
```

#### 9. loadApiKey
```yaml
组件ID: QNA_DRIVER_ANALYSIS.PARSING.LLM.LOAD_API_KEY_V1
文件位置: llm.js:9
组件类型: @SERVICE
功能描述: 从配置中加载API密钥
导出类型: named export
依赖关系: window.LOCAL_CONFIG, fetch API
使用者: LLM调用模块
状态: 已实现，待标准化
注册日期: 2025-08-10
```

#### 10. evaluateConversationWithKimi
```yaml
组件ID: QNA_DRIVER_ANALYSIS.PARSING.LLM.EVALUATE_CONVERSATION_WITH_KIMI_V1
文件位置: llm.js:26
组件类型: @SERVICE
功能描述: 使用Kimi API评估对话质量
导出类型: named export
依赖关系: defaultHeaders, fetch API
使用者: 对话分析模块
状态: 已实现，待标准化
注册日期: 2025-08-10
```

### UI层组件

#### 11. createChartConfigs
```yaml
组件ID: QNA_DRIVER_ANALYSIS.UI.CHARTS.CREATE_CHART_CONFIGS_V1
文件位置: charts.js:7
组件类型: @UTIL
功能描述: 创建ECharts图表配置对象
导出类型: named export
依赖关系: 无
使用者: 图表初始化模块
状态: 已实现，待标准化
注册日期: 2025-08-10
```

#### 12. initializeCharts
```yaml
组件ID: QNA_DRIVER_ANALYSIS.UI.CHARTS.INITIALIZE_CHARTS_V1
文件位置: charts.js:38
组件类型: @SERVICE
功能描述: 初始化所有图表实例
导出类型: named export
依赖关系: window.echarts, createChartConfigs
使用者: 应用初始化模块
状态: 已实现，待标准化
注册日期: 2025-08-10
```

#### 13. updateChartsData
```yaml
组件ID: QNA_DRIVER_ANALYSIS.UI.CHARTS.UPDATE_CHARTS_DATA_V1
文件位置: charts.js:67
组件类型: @SERVICE
功能描述: 更新图表数据和显示
导出类型: named export
依赖关系: ECharts实例
使用者: 数据处理模块
状态: 已实现，待标准化
注册日期: 2025-08-10
```

## 待实现组件 [RS:3]

### CORE层组件

#### 14. CentralRegistry
```yaml
组件ID: QNA_DRIVER_ANALYSIS.CORE.REGISTRY.CENTRAL_REGISTRY_V1
文件位置: registry.js (待创建)
组件类型: @MANAGER
功能描述: 中央组件注册管理器
导出类型: named export (class)
依赖关系: 无
使用者: 所有模块
状态: 待实现
预计完成: 2025-08-10
```

### UI层组件

#### 15. FileUploadHandler
```yaml
组件ID: QNA_DRIVER_ANALYSIS.UI.UPLOAD.FILE_UPLOAD_HANDLER_V1
文件位置: drag-upload.js (待重新实现)
组件类型: @SERVICE
功能描述: 文件上传和拖拽处理服务
导出类型: named export
依赖关系: DOM API, File API
使用者: 主应用模块
状态: 待实现
预计完成: 2025-08-13
```

## 组件接口规范 [RS:4]

### 标准接口格式

#### 函数组件接口
```javascript
/**
 * @NAMESPACE {完整命名空间}
 * @COMPONENT_ID {唯一组件标识}
 * @{组件类型标签}
 * @DESCRIPTION {功能描述}
 * 
 * @param {Type} paramName - 参数描述
 * @returns {Type} 返回值描述
 * @throws {ErrorType} 异常描述
 */
export const functionName = (param1, param2) => {
  // 实现逻辑
};
```

#### 类组件接口
```javascript
/**
 * @NAMESPACE {完整命名空间}
 * @COMPONENT_ID {唯一组件标识}
 * @MANAGER
 * @DESCRIPTION {功能描述}
 */
export class ClassName {
  /**
   * @LIFECYCLE 构造函数
   * @param {Type} param - 参数描述
   */
  constructor(param) {
    // 初始化逻辑
  }

  /**
   * @SERVICE 服务方法
   * @param {Type} param - 参数描述
   * @returns {Type} 返回值描述
   */
  methodName(param) {
    // 方法实现
  }
}
```

### 依赖声明格式
```javascript
/**
 * @DEPENDENCIES
 * - INPUT: {输入依赖列表}
 * - OUTPUT: {输出接口列表}
 * - EXTERNAL: {外部依赖列表}
 */
```

## 组件状态管理 [RS:3]

### 状态类型
- **已实现**: 功能完整，正常工作
- **待标准化**: 功能完整，需要添加标准化头部和注册
- **待实现**: 尚未实现，需要开发
- **开发中**: 正在开发过程中
- **测试中**: 开发完成，正在测试
- **已废弃**: 不再使用，计划移除
- **已迁移**: 已迁移到新的命名空间或实现

### 状态转换规则
```
待实现 → 开发中 → 测试中 → 已实现
已实现 → 待标准化 → 已实现 (标准化完成)
已实现 → 已废弃 → 已移除
已实现 → 已迁移 (命名空间变更)
```

### 状态跟踪
每个组件的状态变更都需要记录：
- 变更时间
- 变更原因
- 负责人
- 影响评估

## 组件质量标准 [RS:3]

### 代码质量
- **命名规范**: 遵循统一的命名规范
- **注释完整**: 包含完整的JSDoc注释
- **错误处理**: 适当的错误处理和异常管理
- **性能考虑**: 考虑性能影响和优化

### 接口质量
- **接口清晰**: 接口定义清晰明确
- **参数验证**: 适当的参数验证
- **返回一致**: 返回值格式一致
- **异常处理**: 统一的异常处理机制

### 文档质量
- **功能描述**: 清晰的功能描述
- **使用示例**: 提供使用示例
- **依赖说明**: 明确的依赖关系说明
- **变更记录**: 完整的变更历史记录

## 组件审查清单 [RS:3]

### 注册前检查
- [ ] 命名空间唯一性验证
- [ ] 功能重复性检查
- [ ] 依赖关系合规性验证
- [ ] 接口规范符合性检查
- [ ] 文档完整性验证

### 定期审查
- [ ] 组件使用情况分析
- [ ] 性能影响评估
- [ ] 依赖关系优化
- [ ] 接口演进规划
- [ ] 废弃组件清理
