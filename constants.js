/**
 * @NAMESPACE QNA_DRIVER_ANALYSIS.DATA.CONSTANTS
 * @COMPONENT_ID CONSTANTS_MODULE_V1
 * @VERSION 1.0.0
 * @CREATED 2024-06-13
 * @UPDATED 2025-08-10
 * @AUTHOR jc-yap89
 * @DESCRIPTION 全局常量配置文件，定义系统级常量和配置参数
 *
 * @DEPENDENCIES
 * - INPUT: 无
 * - OUTPUT: MAX_CONCURRENCY, CSV_MIME, defaultHeaders
 * - EXTERNAL: 无
 *
 * @ARCHITECTURE_LAYER DATA
 * @RELATED_MODULES storage.js, llm.js, parser.js
 *
 * @LIFECYCLE_STATUS STABLE
 * @MIGRATION_STATUS COMPLETED
 *
 * @TAGS @CONFIG_FILE @SHARED_CONSTANT @UTIL
 */

// @SHARED_CONSTANT 并发处理上限
// 定义系统同时处理的最大任务数量，防止API调用超限和浏览器性能问题
export const MAX_CONCURRENCY = 10;

// @SHARED_CONSTANT CSV文件MIME类型
// 用于文件导出时设置正确的Content-Type头部
export const CSV_MIME = "text/csv";

// @UTIL 默认HTTP请求头生成器
// 为API调用生成标准的请求头，包含认证信息和内容类型
export const defaultHeaders = (apiKey) => ({
  "Content-Type": "application/json", // 指定请求体为JSON格式
  Authorization: `Bearer ${apiKey}`,   // Kimi API认证令牌
});

// @REGISTRATION 注册组件到中央注册系统
// 在模块加载时自动注册所有导出的组件
if (typeof window !== 'undefined' && window.centralRegistry) {
  // 注册MAX_CONCURRENCY常量
  window.centralRegistry.registerComponent({
    namespace: 'QNA_DRIVER_ANALYSIS.DATA.CONSTANTS.MAX_CONCURRENCY_V1',
    componentId: 'MAX_CONCURRENCY_V1',
    filePath: 'constants.js',
    type: '@SHARED_CONSTANT',
    description: '并发处理上限常量',
    dependencies: [],
    status: 'STABLE'
  });

  // 注册CSV_MIME常量
  window.centralRegistry.registerComponent({
    namespace: 'QNA_DRIVER_ANALYSIS.DATA.CONSTANTS.CSV_MIME_V1',
    componentId: 'CSV_MIME_V1',
    filePath: 'constants.js',
    type: '@SHARED_CONSTANT',
    description: 'CSV文件MIME类型常量',
    dependencies: [],
    status: 'STABLE'
  });

  // 注册defaultHeaders工具函数
  window.centralRegistry.registerComponent({
    namespace: 'QNA_DRIVER_ANALYSIS.DATA.CONSTANTS.DEFAULT_HEADERS_V1',
    componentId: 'DEFAULT_HEADERS_V1',
    filePath: 'constants.js',
    type: '@UTIL',
    description: '默认HTTP请求头生成器',
    dependencies: [],
    status: 'STABLE'
  });

  console.log('✅ constants.js 组件已注册到中央注册系统');
}
