/**
 * @NAMESPACE QNA_DRIVER_ANALYSIS.PARSING.PARSER
 * @COMPONENT_ID PARSER_MODULE_V1
 * @VERSION 1.0.0
 * @CREATED 2024-06-13
 * @UPDATED 2025-08-10
 * @AUTHOR jc-yap89
 * @DESCRIPTION 文本解析和对话处理服务，负责解析txt文件内容为标准对话结构
 *
 * @DEPENDENCIES
 * - INPUT: 文本内容字符串、文件名
 * - OUTPUT: parseTimestamp, parseTxtContent
 * - EXTERNAL: Date API, console
 *
 * @ARCHITECTURE_LAYER PARSING
 * @RELATED_MODULES llm.js, storage.js, constants.js
 *
 * @LIFECYCLE_STATUS STABLE
 * @MIGRATION_STATUS COMPLETED
 *
 * @TAGS @SERVICE @UTIL @PARSER
 */

// @UTIL 解析时间戳工具函数
// 将日期字符串(DD/MM/YYYY)和时间字符串(HH:mm:ss)转换为时间戳
export const parseTimestamp = (dateStr, timeStr) => {
	if (!dateStr || !timeStr) return 0;
	try {
		const [day, month, year] = dateStr.split('/');
		const [hour, minute, second] = timeStr.split(':');
		const date = new Date(
			parseInt(year),
			parseInt(month) - 1,
			parseInt(day),
			parseInt(hour),
			parseInt(minute),
			parseInt(second) || 0
		);
		return date.getTime();
	} catch (e) {
		console.warn('Failed to parse timestamp:', dateStr, timeStr);
		return 0;
	}
};

// @SERVICE 解析txt文件内容为标准对话结构
// 支持分隔符格式，提取对话消息、时间戳、角色等信息
export const parseTxtContent = (content, fileName) => {
	const conversations = [];
	let driverName = fileName.replace('.txt', '').replace(/\s*\(New\)$/i, '').trim();

	// 按分隔符分割消息块
	const messageBlocks = content.split(/\n?\-{40,}\n?/).filter(block => block.trim());

	messageBlocks.forEach(block => {
		const lines = block.split(/\r?\n/).map(line => line.trim()).filter(line => line);

		let time = '';
		let date = '';
		let role = '';
		let name = '';
		let msg = '';

		// 解析每个块的内容
		lines.forEach(line => {
			// 匹配时间: Time: 25/01/2025 14:43:35
			const timeMatch = line.match(/^Time:\s*(\d{1,2}\/\d{1,2}\/\d{4})\s+(\d{1,2}:\d{2}:\d{2})$/);
			if (timeMatch) {
				date = timeMatch[1];
				time = timeMatch[2];
				return;
			}

			// 匹配用户: User: Driver: Chai Chang Chin 或 User: Support: CS Jiahui
			const userMatch = line.match(/^User:\s+(Driver|Support):\s*(.+)$/);
			if (userMatch) {
				role = userMatch[1];
				name = userMatch[2].trim();
				return;
			}

			// 匹配消息: Message: 内容
			const msgMatch = line.match(/^Message:\s*(.*)$/);
			if (msgMatch) {
				msg = msgMatch[1].trim();
				return;
			}

			// 如果不是特定格式，可能是消息的续行
			if (msg && line && !line.match(/^(Time|User|Message):/)) {
				msg += '\n' + line;
			}
		});

		// 如果解析到完整的消息，添加到对话列表
		if (time && role && name) {
			conversations.push({
				time,
				date: date || new Date().toISOString().slice(0, 10),
				role,
				name: name.trim(),
				msg: msg || '',
				driverName,
				timestamp: parseTimestamp(date, time)
			});
		}
	});

	// 按时间戳排序确保对话顺序正确
	conversations.sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0));

	return conversations;
};

// @SERVICE 解析单个对话文件
export const parseConversationFile = async (file) => {
	try {
		const text = await file.text();
		const conversations = parseTxtContent(text, file.name);
		return {
			fileName: file.name,
			driverName: file.name.replace('.txt', ''),
			conversations,
		};
	} catch (error) {
		console.error(`解析文件 ${file.name} 失败:`, error);
		throw error;
	}
};

// @REGISTRATION 注册组件到中央注册系统
// 在模块加载时自动注册所有导出的组件
if (typeof window !== 'undefined' && window.centralRegistry) {
  // 注册parseTimestamp工具函数
  window.centralRegistry.registerComponent({
    namespace: 'QNA_DRIVER_ANALYSIS.PARSING.PARSER.PARSE_TIMESTAMP_V1',
    componentId: 'PARSE_TIMESTAMP_V1',
    filePath: 'parser.js',
    type: '@UTIL',
    description: '解析时间戳工具函数',
    dependencies: [
      { target: 'Date', type: 'EXTERNAL', description: '浏览器Date API' }
    ],
    status: 'STABLE'
  });

  // 注册parseTxtContent服务函数
  window.centralRegistry.registerComponent({
    namespace: 'QNA_DRIVER_ANALYSIS.PARSING.PARSER.PARSE_TXT_CONTENT_V1',
    componentId: 'PARSE_TXT_CONTENT_V1',
    filePath: 'parser.js',
    type: '@SERVICE',
    description: '解析txt文件内容为标准对话结构',
    dependencies: [
      { target: 'PARSE_TIMESTAMP_V1', type: 'REQUIRED', description: '时间戳解析工具' }
    ],
    status: 'STABLE'
  });

  console.log('✅ parser.js 组件已注册到中央注册系统');
}
