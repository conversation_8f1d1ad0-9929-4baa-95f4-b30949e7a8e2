/**
 * parser.js - 文本解析和对话处理服务
 * 负责解析 txt 文件内容为标准对话结构
 */

// @UTIL 解析时间戳 (DD/MM/YYYY + HH:mm:ss)
export const parseTimestamp = (dateStr, timeStr) => {
	if (!dateStr || !timeStr) return 0;
	try {
		const [day, month, year] = dateStr.split('/');
		const [hour, minute, second] = timeStr.split(':');
		const date = new Date(
			parseInt(year),
			parseInt(month) - 1,
			parseInt(day),
			parseInt(hour),
			parseInt(minute),
			parseInt(second) || 0
		);
		return date.getTime();
	} catch (e) {
		console.warn('Failed to parse timestamp:', dateStr, timeStr);
		return 0;
	}
};

// @UTIL 解析单个 txt 内容（支持分隔符格式）
export const parseTxtContent = (content, fileName) => {
	const conversations = [];
	let driverName = fileName.replace('.txt', '').replace(/\s*\(New\)$/i, '').trim();

	// 按分隔符分割消息块
	const messageBlocks = content.split(/\n?\-{40,}\n?/).filter(block => block.trim());

	messageBlocks.forEach(block => {
		const lines = block.split(/\r?\n/).map(line => line.trim()).filter(line => line);

		let time = '';
		let date = '';
		let role = '';
		let name = '';
		let msg = '';

		// 解析每个块的内容
		lines.forEach(line => {
			// 匹配时间: Time: 25/01/2025 14:43:35
			const timeMatch = line.match(/^Time:\s*(\d{1,2}\/\d{1,2}\/\d{4})\s+(\d{1,2}:\d{2}:\d{2})$/);
			if (timeMatch) {
				date = timeMatch[1];
				time = timeMatch[2];
				return;
			}

			// 匹配用户: User: Driver: Chai Chang Chin 或 User: Support: CS Jiahui
			const userMatch = line.match(/^User:\s+(Driver|Support):\s*(.+)$/);
			if (userMatch) {
				role = userMatch[1];
				name = userMatch[2].trim();
				return;
			}

			// 匹配消息: Message: 内容
			const msgMatch = line.match(/^Message:\s*(.*)$/);
			if (msgMatch) {
				msg = msgMatch[1].trim();
				return;
			}

			// 如果不是特定格式，可能是消息的续行
			if (msg && line && !line.match(/^(Time|User|Message):/)) {
				msg += '\n' + line;
			}
		});

		// 如果解析到完整的消息，添加到对话列表
		if (time && role && name) {
			conversations.push({
				time,
				date: date || new Date().toISOString().slice(0, 10),
				role,
				name: name.trim(),
				msg: msg || '',
				driverName,
				timestamp: parseTimestamp(date, time)
			});
		}
	});

	// 按时间戳排序确保对话顺序正确
	conversations.sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0));

	return conversations;
};

// @SERVICE 解析单个对话文件
export const parseConversationFile = async (file) => {
	try {
		const text = await file.text();
		const conversations = parseTxtContent(text, file.name);
		return {
			fileName: file.name,
			driverName: file.name.replace('.txt', ''),
			conversations,
		};
	} catch (error) {
		console.error(`解析文件 ${file.name} 失败:`, error);
		throw error;
	}
};
