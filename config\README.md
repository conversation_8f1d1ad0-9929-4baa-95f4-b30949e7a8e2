# API 密钥配置指南

本应用支持多种方式配置 API 密钥。优先级顺序：`config/api-keys.json` > `config/local-config.js` > 本地存储

## 方法一：使用 JSON 配置文件（推荐）

### 使用步骤

1. **编辑配置文件**
   - 打开 `config/api-keys.json`
   - 将 `"kimi": "sk-your-kimi-api-key"` 中的示例值替换为真实的 Kimi API 密钥

```json
{
  "apiKeys": {
    "kimi": "sk-your-real-kimi-api-key-here",
    "gpt": "sk-your-openai-api-key",
    "claude": "your-anthropic-api-key"
  },
  "defaultProvider": "kimi"
}
```

2. **运行应用**
   - 使用浏览器打开 `index.html`
   - 应用会自动加载 API 密钥

### 注意事项
- **不要将真实密钥提交到代码仓库**
- 建议在本地开发时使用，生产环境建议使用环境变量

## 方法二：使用本地脚本配置

### 使用步骤

1. **复制示例文件**
   - 将 `config/local-config.example.js` 复制为 `config/local-config.js`

2. **编辑配置**

```javascript
window.LOCAL_CONFIG = {
  // 二选一：
  apiKey: 'sk-your-kimi-key',
  apiKeys: { kimi: 'sk-your-kimi-key' }
};
```

3. **在 HTML 中引用**（可选）

```html
<script src="./config/local-config.js"></script>
```

## 获取 Kimi API 密钥

1. 访问 [月之暗面开放平台](https://platform.moonshot.cn/)
2. 注册并登录账户
3. 在 API 密钥管理页面创建新的密钥
4. 复制密钥到配置文件中

## 故障排除

### 常见错误

- **"API Key未提供"**：请检查配置文件是否正确设置
- **"请配置真实的 Kimi API Key"**：当前使用的是示例密钥，需要替换为真实密钥
- **401 错误**：API 密钥无效或已过期，请检查密钥是否正确

### 配置验证

应用启动时会在日志中显示：
- ✅ `已自动加载API Key` - 配置成功
- ❌ `未找到有效的API Key` - 需要配置密钥

## 安全建议

- 不要在代码仓库中存储真实密钥
- 定期更换 API 密钥
- 使用环境变量管理敏感信息
