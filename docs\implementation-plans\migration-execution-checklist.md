# 迁移执行清单（不增代码量｜纯前端｜扁平结构）

更新时间：2025-08-10

目的：将 `standalone.html` 内联 JS 按模块批次“剪切-粘贴”迁移到既有文件（`constants.js / storage.js / charts.js / parser.js / drag-upload.js / main.js`），不改变功能和行为，不引入打包器/后端，避免 CORS。

---

## 全局约束与假设

- 行为零变化：所有迁移仅发生在实现位置，不改调用路径、参数和返回。
- 净新增≈0：每批迁移后“standalone.html 删除行数 ≈ 新文件新增行数”，允许少量 import/export 样板。
- CDN 全局库：仍通过 `<script>` 注入 `echarts` 与 `Papa`（全局）。应用模块以 `window.echarts`/`window.Papa` 访问。
- API 调用：仅在 `window.LOCAL_CONFIG.apiKey` 存在时启用；失败时记录到 `#log-box`。
- 保守常量：如 `MAX_CONCURRENCY` 目前内联为 10，迁移时保持 10 以维持行为（后续若需对齐 50，再单独 PR）。

---

## 模块映射（基于 standalone.html 中分节注释）

- constants.js
  - MAX_CONCURRENCY、CSV_MIME、选择器 id/状态枚举（如 FILE_STATUS/LLM_ENGINES/COMPLEXITY_LEVELS/QUESTION_TAGS 等常量）
  - defaultHeaders(config) 等轻量纯函数
- storage.js
  - STORAGE_KEYS、LEGACY_ENHANCED_KEYS
  - StorageManager（localStorage 读写、CSV 导出；内部使用 `window.Papa`）
  - AppDataManager（合并/更新/导出入口，保持 `qna_*` 命名空间）
- charts.js
  - initializeCharts() 创建四图表实例；updateChartsData(data)
  - 预览表格填充函数（questions/effectiveness/satisfaction/knowledge/qa-*）
- parser.js
  - parseTxtContent(content, fileName)、parseTimestamp(...)
  - evaluateConversationWithKimi(...)、callKimiAPI(...)、parseKimiResponse(...)、repairAndParseJSON(...)
  - processAnalysisResult(...)
- drag-upload.js
  - 文件选择/拖拽/粘贴事件、文件列表渲染、updateStartButtonState()
- main.js（编排）
  - 日志 log(...)、全局实例创建：fileRegistry、progressManager、apiConfigManager、dualLLMManager、queueManager、appDataManager、globalCharts、reportGenerator
  - 绑定 UI 事件（start/pause/resume/status/export/clear、tab 切换、QA 优化、报告）
  - ConcurrentQueueManager 桥接（或内联队列逻辑，如现有）
- 可选：report.js（ReportGenerator）、report-exporter.js（ReportExporter）、qa-optimize.js（QATagAnalyzer/QADeduplicationManager/QAOptimizationManager）

---

## 批次计划（每批 200–400 行，批后烟测）

### 批次 1：常量与存储（低风险）

- 剪切到 constants.js：
  - MAX_CONCURRENCY、CSV_MIME、QUESTION_TAGS、FILE_STATUS、LLM_ENGINES、COMPLEXITY_LEVELS、PRIORITY_LEVELS 等常量块
  - defaultHeaders(apiKey)
- 剪切到 storage.js：
  - STORAGE_KEYS、LEGACY_ENHANCED_KEYS
  - StorageManager（localStorage 读写）、CSV 导出相关（`Papa.unparse`）
  - AppDataManager（数据聚合/导出接口；维持 `qna_*` 前缀）
- HTML 调整：将等量实现从 `<script>` 中删除；保留调用不变。
- 兼容桥接（临时）：必要时在 storage.js 末尾执行 `window.AppDataManager = AppDataManager;` 等，待后续入口模块化后移除。
- 验收：启动页面、导出 CSV 正常；localStorage 可读写；控制台无新错误。

### 批次 2：图表模块

- 剪切到 charts.js：initializeCharts、updateChartsData、预览表格填充函数。
- 维护 DOM id 不变：`chart-questions/effectiveness/satisfaction/knowledge` 等。
- 验收：上传小样本后四图表初始化正常；预览表格渲染正确。

### 批次 3：解析与 AI 调用

- 剪切到 parser.js：parseTxtContent、parseTimestamp、evaluateConversationWithKimi、callKimiAPI、parseKimiResponse、repairAndParseJSON、processAnalysisResult。
- 保持“JSON 优先 + 正则兜底”策略；仅移动实现，不改逻辑。
- 验收：相同输入文件，识别/评分/提取结果与迁移前一致（基于日志与结果表）。

### 批次 4：上传与任务编排（入口导线最小化）

- 剪切到 drag-upload.js：拖拽/选择/列表渲染、按钮状态维护。
- main.js：
  - 创建核心实例；绑定事件；封装启动流程；桥接 ConcurrentQueueManager。
  - 将现有 `log`/状态面板控制迁入。
- HTML：在底部新增一次 `<script type="module" src="./main.js"></script>`（如需）；保持 CDN `<script>` 在前。
- 验收：选择文件、开始/暂停/恢复、状态面板均可用；进度条更新正常。

### 批次 5（可选）：报告与问答优化

- report.js：ReportGenerator 及相关串接；report-exporter.js：ReportExporter。
- qa-optimize.js：QATagAnalyzer、QADeduplicationManager、QAOptimizationManager。
- 验收：报告生成/导出、优化流程（标签/去重）与历史记录功能正常。

---

## 执行步骤模板（每批）

1. 精确剪切：按 `// ==================== SECTION ====================` 标记块剪切到目标模块。
2. 导出/桥接：模块采用具名导出；必要时临时挂到 `window.*` 以避免未迁移段访问失败。
3. HTML 清理：删除对应内联实现（保持调用点不变）。
4. 烟测：

- 打开 HTML（或 file:// 受限时用本地静态服务）。
- 选 1–2 个小 .txt 文件，验证解析、图表、导出、日志。
- 控制台无新错误，网络无意外 CORS 失败。

1. 记账：

- 删除行数 / 新增行数 / 样板行（import/export/桥接）。

---

## 回滚策略

- 每批迁移以小提交结束；若失败，回滚至上一提交（standalone.html 内联实现仍在历史中）。
- 桥接期允许 `window.*`，保证旧代码可访问新实现；批次完成后逐步移除桥接。

---

## 烟测脚本（人工步骤）

- 加载页面，确认无 JS 报错。
- 拖拽或选择 2 个样例 `.txt`（来自 `New folder/`）。
- 观察：
  - 进度卡片显示、百分比递增；
  - 实时表格出现行；
  - 四图表初始化不报错；
  - 导出 CSV 可下载且编码正确（含 BOM）。
- 若设置 `config/local-config.js`，触发一次 Kimi 调用，观测日志与结果解析。

---

## 打开问题与确认项

- MAX_CONCURRENCY 暂时保留 10；是否在迁移完成后对齐到 50？
- AppDataManager 归属：按现状放在 storage.js，后续是否拆分 `data-manager.js`？
- Report/QA 优化是否拆分为独立文件（report.js / qa-optimize.js），还是与 main.js 同步合并？

---

## 验收门槛（批次级）

- Requirements：纯前端（是）、规避 CORS（是）、扁平结构（是）。
- Quality gates：构建（N/A，静态）、Lint（通过基本 Markdown/风格检查即可）、单元测试（无）、烟测（手动 PASS）。
- 日志/错误：无新增未捕获异常；失败路径有清晰日志。
