{"llms": {"kimi": {"provider": "moonshot", "model": "kimi-k2-turbo-preview", "baseUrl": "https://api.moonshot.cn/v1/chat/completions", "apiKey": "sk-JcWzWW8FEnbrEvmvIY6N4vbcFzPxqXJBiFSbqej3Ff2uJBsY", "apiKeyEnv": "KIMI_API_KEY", "request": {"temperature": 0.2, "top_p": 0.95, "max_tokens": 8000, "timeout_ms": 60000}, "enabled": true}, "gemini": {"provider": "google", "model": "gemini-2.5-flash", "baseUrl": "https://generativelanguage.googleapis.com/v1beta", "apiKey": "AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s", "apiKeyEnv": "GEMINI_API_KEY", "request": {"temperature": 0.2, "top_p": 0.95, "max_output_tokens": 8000, "timeout_ms": 60000}, "enabled": false}}, "apiKeys": {"kimi": "sk-your-kimi-api-key", "gemini": "AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s"}, "defaults": {"primary": "kimi", "maxConcurrency": 10}, "notes": {"kimi": "kimi-k2-turbo-preview", "gemini": "gemini-2.5-flash"}}