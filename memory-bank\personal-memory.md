# 用户偏好和个人详细信息

**用户标识**: jc-yap89  
**邮箱**: <EMAIL>  
**最后更新**: 2025-08-10  
**相关性评分**: [RS:4]

## 用户基本信息 [RS:4]

### 开发者档案
- **GitHub用户名**: jc-yap89
- **主要邮箱**: <EMAIL>
- **工作时区**: 未明确指定 (推测为亚洲时区)
- **主要语言**: 中文 (简体)
- **技术背景**: 前端开发、JavaScript、Web技术

### 项目角色
- **当前项目**: GoMyHire 司机客服对话分析平台
- **角色**: 主要开发者、架构设计者
- **工作模式**: 独立开发 + AI助手协作
- **责任范围**: 全栈前端开发、架构设计、项目管理

## 沟通偏好 [RS:5]

### 语言偏好
- **主要沟通语言**: 中文 (简体)
- **代码注释语言**: 中文
- **文档编写语言**: 中文
- **变量命名**: 英文 (遵循国际惯例)

### 沟通风格
- **详细程度**: 喜欢详细的解释和分析
- **结构化**: 偏好结构化、有条理的信息组织
- **实用性**: 重视实际可执行的解决方案
- **渐进式**: 喜欢分步骤、可跟踪的执行计划

### 反馈方式
- **进度报告**: 喜欢阶段性的进度更新和确认
- **问题讨论**: 倾向于深入分析问题的根本原因
- **决策参与**: 希望参与重要技术决策的讨论
- **质量关注**: 高度重视代码质量和架构一致性

## 技术偏好 [RS:4]

### 编程风格
- **模块化**: 强烈偏好模块化和组件化设计
- **标准化**: 重视代码标准化和一致性
- **文档化**: 要求详细的代码注释和文档
- **防御性编程**: 重视错误处理和边界情况处理

### 架构偏好
- **分层架构**: 偏好清晰的分层架构设计
- **单一职责**: 坚持单一职责原则
- **依赖管理**: 重视依赖关系的清晰管理
- **可维护性**: 优先考虑长期可维护性

### 技术选择
- **原生优先**: 偏好使用原生技术而非复杂框架
- **简单部署**: 重视部署的简单性和便利性
- **性能考虑**: 关注性能优化和用户体验
- **兼容性**: 重视浏览器兼容性和标准遵循

## 工作习惯 [RS:3]

### 开发流程
- **规划优先**: 喜欢在编码前进行详细规划
- **渐进实施**: 偏好分阶段、可验证的实施方式
- **质量检查**: 每个阶段都进行质量检查和验证
- **文档同步**: 要求代码和文档同步更新

### 时间管理
- **任务分解**: 喜欢将大任务分解为小的可执行项目
- **优先级管理**: 重视任务优先级的明确定义
- **进度跟踪**: 需要清晰的进度跟踪和里程碑
- **检查点设置**: 定期设置检查点评估进展

### 协作方式
- **AI助手协作**: 善于与AI助手进行技术协作
- **自主决策**: 在技术细节上倾向于自主决策
- **反馈接受**: 开放接受建设性的技术建议
- **知识分享**: 愿意分享技术经验和最佳实践

## 项目特定偏好 [RS:4]

### 当前项目关注点
- **架构完整性**: 高度重视统一命名空间和架构管理
- **防护机制**: 强调防重复开发、防架构破坏的重要性
- **标准化**: 要求所有代码都遵循统一的标准和规范
- **可追溯性**: 重视变更的可追溯性和审计能力

### 质量标准
- **零容忍**: 对架构违规和命名冲突零容忍
- **完整性**: 要求功能实现的完整性和一致性
- **文档一致**: 要求文档与代码实现完全一致
- **测试验证**: 重视功能测试和回归测试

### 技术约束接受度
- **纯前端限制**: 完全接受并支持纯前端架构约束
- **CORS规避**: 理解并支持CORS规避的技术方案
- **性能权衡**: 理解性能与功能之间的权衡关系
- **兼容性要求**: 接受现代浏览器兼容性的限制

## 学习和成长偏好 [RS:3]

### 技术学习
- **深度优先**: 偏好深入理解技术原理而非表面应用
- **实践导向**: 通过实际项目学习和掌握新技术
- **最佳实践**: 重视学习和应用行业最佳实践
- **持续改进**: 持续关注技术发展和改进机会

### 知识管理
- **结构化记录**: 喜欢结构化的知识记录和管理
- **经验总结**: 重视项目经验的总结和沉淀
- **模式识别**: 善于识别和抽象技术模式
- **知识复用**: 重视知识和经验的复用价值

## 决策模式 [RS:3]

### 技术决策
- **数据驱动**: 基于客观数据和分析做决策
- **长期考虑**: 优先考虑长期影响而非短期便利
- **风险评估**: 重视风险识别和缓解措施
- **可逆性**: 偏好可逆的技术决策

### 优先级判断
- **架构优先**: 架构完整性优于功能快速实现
- **质量优先**: 代码质量优于开发速度
- **标准优先**: 标准一致性优于个人偏好
- **长远优先**: 长期可维护性优于短期便利

## 沟通触发器 [RS:4]

### 需要确认的情况
- 重要架构决策变更
- 可能影响现有功能的修改
- 新增外部依赖或技术选择
- 偏离既定计划的调整

### 需要详细解释的情况
- 复杂的技术实现方案
- 架构设计的权衡考虑
- 性能优化的具体措施
- 错误处理和异常情况

### 需要进度报告的情况
- 每个主要阶段完成后
- 遇到阻塞或风险时
- 发现重要问题时
- 需要调整计划时

## 成功定义 [RS:4]

### 项目成功标准
- **功能完整**: 所有规划功能都正确实现
- **架构健康**: 架构一致性和完整性达标
- **质量达标**: 代码质量和文档质量达标
- **用户满意**: 最终用户使用体验良好

### 个人成长目标
- **架构能力**: 提升系统架构设计能力
- **工程实践**: 掌握更多工程化最佳实践
- **问题解决**: 增强复杂问题的分析和解决能力
- **技术视野**: 扩展前端技术的深度和广度
