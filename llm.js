/**
 * @NAMESPACE QNA_DRIVER_ANALYSIS.PARSING.LLM
 * @COMPONENT_ID LLM_MODULE_V1
 * @VERSION 1.0.0
 * @CREATED 2024-06-13
 * @UPDATED 2025-08-10
 * @AUTHOR jc-yap89
 * @DESCRIPTION LLM调用与响应处理服务，负责与Kimi API交互进行对话质量分析
 *
 * @DEPENDENCIES
 * - INPUT: 对话数据、API密钥
 * - OUTPUT: loadApiKey, evaluateConversationWithKimi
 * - EXTERNAL: fetch API, window.LOCAL_CONFIG, constants.js
 *
 * @ARCHITECTURE_LAYER PARSING
 * @RELATED_MODULES constants.js, parser.js, storage.js
 *
 * @LIFECYCLE_STATUS STABLE
 * @MIGRATION_STATUS COMPLETED
 *
 * @TAGS @SERVICE @API_CLIENT @AI_INTEGRATION
 */

import { defaultHeaders } from './constants.js';

// @SERVICE API密钥加载服务
// 从本地配置或配置文件中加载Kimi API密钥
export const loadApiKey = async () => {
  try {
    if (window.LOCAL_CONFIG) {
      const explicit = window.LOCAL_CONFIG.apiKey || window.LOCAL_CONFIG.apiKeys?.kimi;
      if (explicit) return explicit;
    }
    // 兼容 config/api-keys.json（如果通过 http(s) 访问）
    const resp = await fetch('./config/api-keys.json');
    if (resp.ok) {
      const json = await resp.json();
      return json?.kimi?.apiKey || json?.apiKey || '';
    }
  } catch (_) {}
  return '';
};

// @SERVICE 对话质量分析服务
// 使用Kimi API对对话进行智能分析，支持多问题识别和质量评估
export const evaluateConversationWithKimi = async (conversations, apiKey) => {
  if (!conversations || conversations.length === 0) {
    throw new Error('未找到有效对话内容');
  }
  const conversationText = conversations.map((c) => `${c.role}（${c.name}）: ${c.msg}`).join('\n');
  const prompt = `你是一名联系中心对话质量分析专家。请分析以下"客服-司机"对话，智能识别其中包含的独立问题数量，并进行深度评估。

【核心任务】
1. 仔细阅读完整对话，识别其中包含的独立问题（一个问题 = 司机的一个具体诉求或关切点）
2. 如果对话只包含一个问题，返回单个JSON对象
3. 如果对话包含多个独立问题，返回JSON数组，每个元素对应一个问题的分析
4. 从每个问题中提取标准化的问答题集数据，构建司机知识库

【问题识别标准】
- 独立问题：司机提出的不同类型的诉求、咨询或投诉
- 相关问题：围绕同一核心问题的补充说明或追问，应合并为一个问题
- 时间跨度：考虑对话的时间跨度，长时间间隔后的新话题通常是新问题

【输入对话】
${conversationText}

【分析要求】
- 深度分析每个问题的背景、客服响应质量、解决效果、司机满意度
- 如果同一问题有多个客服参与，请综合评估团队协作效果
- 重点关注问题的实际解决程度，而非仅仅是回复的及时性
- 提取可操作的实用知识点，帮助其他司机解决类似问题

【问答题集提取要求】
- 识别可复用的通用问题（去除个人信息，如具体订单号、姓名等）
- 将客服回答标准化为清晰的操作步骤
- 只提取有明确解决方案的问答，过滤寒暄和无实质内容的对话
- 确保答案的准确性和可操作性
- 自动判断问题的通用性和重要性

【输出格式】
如果只有一个问题，返回单个JSON对象：
{
  "question": "问题详细描述",
  "questionBrief": "问题简述",
  "questionTags": ["标签1", "标签2"],
  "supportAgent": "主要客服姓名",
  "responseTime": 85,
  "resolutionRate": 90,
  "effectiveness": 88,
  "efficiency": 85,
  "satisfaction": 75,
  "attitude": 92,
  "knowledge": "实用知识点1；知识点2",
  "complexity": 3,
  "priority": 3,
  "extractedQA": {
    "question": "通用问题描述（去除个人信息）",
    "answer": "详细的标准答案",
    "operationSteps": ["步骤1", "步骤2", "步骤3"],
    "applicableScenarios": "适用场景描述",
    "difficulty": 2,
    "isCommon": true
  }
}

如果有多个问题，返回JSON数组：
[
  { /* 第一个问题的完整分析结果，包含extractedQA */ },
  { /* 第二个问题的完整分析结果，包含extractedQA */ },
  ...
]

【字段说明】
- question: string - 司机问题的完整详细描述（20-100字，包含背景和具体诉求）
- questionBrief: string - 问题核心要点简述（8-25字）
- questionTags: array - 问题标签，从以下选择2-5个：["薪资福利","订单派单","车辆维护","路线导航","客户服务","系统操作","政策咨询","投诉建议","技术故障","紧急求助","培训考核","其他"]
- supportAgent: string - 主要处理客服姓名（多客服时选择贡献最大的）
- responseTime: integer - 响应时间评估（0-100）
- resolutionRate: integer - 问题解决完成率（0-100）
- effectiveness: integer - 回复专业性和有效性（0-100）
- efficiency: integer - 沟通效率和流畅度（0-100）
- satisfaction: integer - 司机满意度评估（0-100）
- attitude: integer - 客服服务态度专业性（0-100）
- knowledge: string - 实用知识点，用"；"分隔
- complexity: integer - 问题复杂度（1-5）
- priority: integer - 问题优先级（1-5）
- extractedQA: object - 提取的问答题集数据
  - question: string - 通用问题描述（去除个人信息，如订单号、姓名等）
  - answer: string - 详细的标准答案
  - operationSteps: array - 具体操作步骤数组
  - applicableScenarios: string - 适用场景描述
  - difficulty: integer - 问题难度等级（1-5）
  - isCommon: boolean - 是否为通用问题

【重要提醒】
- 必须基于对话内容客观分析，避免主观臆断
- 评分要充分利用0-100的范围，避免集中在中等水平
- 知识点要具体实用，能真正帮助其他司机
- 严格按照JSON格式输出，不要任何额外文字

【问答提取质量控制】
- 只提取有明确解决方案的问答，过滤纯粹的寒暄或无实质内容的对话
- 确保答案的准确性和可操作性，避免模糊或不完整的回答
- 将个人化信息（如订单号、姓名、具体时间）替换为通用描述
- 如果对话没有实质性的问答内容，extractedQA可以为null

仅输出JSON对象或JSON数组，不要任何额外文字。`;

  return await callKimiAPI(prompt, apiKey, '统一对话分析');
};

// 统一的Kimi API调用函数
export const callKimiAPI = async (prompt, apiKey, logPrefix = 'API调用') => {
  const maxRetries = 3;
  let lastError;
  let data, content;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      if (!apiKey) apiKey = await loadApiKey();
      if (!apiKey) throw new Error('API Key未提供。请在 config/api-keys.json 或 config/local-config.js 中配置有效的 Kimi API Key');
      if (apiKey === 'sk-your-kimi-api-key') throw new Error('请在 config/api-keys.json 中配置真实的 Kimi API Key，当前使用的是示例密钥');

      if (attempt > 1) {
        const delay = Math.pow(2, attempt - 1) * 1000 + Math.random() * 1000;
        await new Promise(r => setTimeout(r, delay));
      }

      const body = { model: 'kimi-k2-turbo-preview', messages: [{ role: 'user', content: prompt }], temperature: 0.3, stream: false, max_tokens: 8000 };
      const response = await fetch('https://api.moonshot.cn/v1/chat/completions', { method: 'POST', headers: defaultHeaders(apiKey), body: JSON.stringify(body), mode: 'cors' });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API响应错误: ${response.status} - ${errorText}`);
      }
      data = await response.json();
      content = data.choices?.[0]?.message?.content || '';
      break;
    } catch (e) {
      lastError = e;
      const retryable = e.message.includes('响应被截断') || e.message.includes('API响应错误') || e.message.includes('fetch') || e.message.includes('network');
      if (attempt === maxRetries || !retryable) throw e;
    }
  }

  return await parseKimiResponse(content, logPrefix);
};

// 统一的Kimi响应解析函数
export const parseKimiResponse = async (content, logPrefix = 'API响应') => {
  if (!content || content.length < 50) throw new Error(`API响应过短或为空: ${content?.length || 0} 字符`);
  const isLikelyTruncated = (!content.trim().endsWith('}') && !content.trim().endsWith(']') && (content.includes('"question"') || content.includes('"effectiveness"')));
  if (isLikelyTruncated) throw new Error('响应被截断，需要重试');

  let result;
  try { result = JSON.parse(content); } catch (_) { result = await repairAndParseJSON(content, logPrefix); }
  return await processAnalysisResult(result, logPrefix);
};

// JSON修复函数
export const repairAndParseJSON = async (content, logPrefix) => {
  let repaired = content.trim();
  const isArray = repaired.startsWith('[');
  const isObject = repaired.startsWith('{');
  if (isArray && !repaired.endsWith(']')) {
    const lastIdx = repaired.lastIndexOf('}');
    if (lastIdx > -1) repaired = repaired.substring(0, lastIdx + 1) + ']';
  } else if (isObject && !repaired.endsWith('}')) {
    const lastQuote = repaired.lastIndexOf('"');
    if (lastQuote !== -1) {
      const after = repaired.substring(lastQuote + 1).trim();
      repaired = after === '' || after === ',' ? repaired.substring(0, lastQuote + 1) + '}' : repaired + '}';
    } else {
      repaired += '}';
    }
  }
  try { return JSON.parse(repaired); } catch (repairError) {
    const match = content.match(/[\{\[][\s\S]*[\}\]]/);
    if (match) { return JSON.parse(match[0]); }
    throw new Error(`JSON解析失败且无法修复: ${repairError.message}`);
  }
};

// 智能处理分析结果（兼容单问题/多问题）
export const processAnalysisResult = async (result, logPrefix) => {
  const isArray = Array.isArray(result);
  const isObject = typeof result === 'object' && result !== null && !isArray;
  if (isArray) {
    const validatedResults = result.map(item => validateEvaluationResult(item));
    return { type: 'multi-question', totalGroups: validatedResults.length, successfulAnalyses: validatedResults.length, results: validatedResults.map((evaluation, index) => ({ groupIndex: index, conversationGroup: [], evaluation })) };
  } else if (isObject) {
    return validateEvaluationResult(result);
  }
  throw new Error(`无法识别的结果格式: ${typeof result}`);
};

// 运行时从全局取校验器，避免循环依赖；在未就绪时提供兜底
const validateEvaluationResult = (result) => {
  const fn = window.__validateEvaluationResult__;
  if (typeof fn === 'function') return fn(result);
  // 简易兜底，避免因加载顺序导致崩溃
  return {
    question: result?.question || '',
    questionBrief: result?.questionBrief || '',
    questionTags: Array.isArray(result?.questionTags) ? result.questionTags : [],
    supportAgent: result?.supportAgent || '未知客服',
    responseTime: Number(result?.responseTime) || 0,
    resolutionRate: Number(result?.resolutionRate) || 0,
    effectiveness: Number(result?.effectiveness) || 0,
    efficiency: Number(result?.efficiency) || 0,
    satisfaction: Number(result?.satisfaction) || 0,
    attitude: Number(result?.attitude) || 0,
    knowledge: result?.knowledge || '',
    complexity: Math.max(1, Math.min(5, Number(result?.complexity) || 1)),
    priority: Math.max(1, Math.min(5, Number(result?.priority) || 1)),
  };
};

// @REGISTRATION 注册组件到中央注册系统
// 在模块加载时自动注册所有导出的组件
if (typeof window !== 'undefined' && window.centralRegistry) {
  // 注册loadApiKey服务函数
  window.centralRegistry.registerComponent({
    namespace: 'QNA_DRIVER_ANALYSIS.PARSING.LLM.LOAD_API_KEY_V1',
    componentId: 'LOAD_API_KEY_V1',
    filePath: 'llm.js',
    type: '@SERVICE',
    description: 'API密钥加载服务',
    dependencies: [
      { target: 'fetch', type: 'EXTERNAL', description: '浏览器fetch API' },
      { target: 'window.LOCAL_CONFIG', type: 'OPTIONAL', description: '本地配置对象' }
    ],
    status: 'STABLE'
  });

  // 注册evaluateConversationWithKimi服务函数
  window.centralRegistry.registerComponent({
    namespace: 'QNA_DRIVER_ANALYSIS.PARSING.LLM.EVALUATE_CONVERSATION_WITH_KIMI_V1',
    componentId: 'EVALUATE_CONVERSATION_WITH_KIMI_V1',
    filePath: 'llm.js',
    type: '@SERVICE',
    description: '对话质量分析服务',
    dependencies: [
      { target: 'DEFAULT_HEADERS_V1', type: 'REQUIRED', description: '默认HTTP请求头生成器' },
      { target: 'fetch', type: 'EXTERNAL', description: '浏览器fetch API' }
    ],
    status: 'STABLE'
  });

  console.log('✅ llm.js 组件已注册到中央注册系统');
}
