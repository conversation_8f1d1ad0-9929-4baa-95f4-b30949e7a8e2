# 技术栈和开发环境

**技术栈版本**: 1.0.0  
**最后更新**: 2025-08-10  
**维护者**: jc-yap89  
**相关性评分**: [RS:4]

## 核心技术栈 [RS:5]

### 前端技术
```javascript
// 核心语言和标准
JavaScript ES6+ (ES2015-ES2023)
- 原生ES模块 (import/export)
- 异步编程 (async/await, Promise)
- 现代语法特性 (解构、箭头函数、模板字符串等)

HTML5
- 语义化标签
- 现代Web APIs
- 响应式设计

CSS3
- CSS变量 (Custom Properties)
- Flexbox和Grid布局
- 现代选择器和伪类
```

### 第三方依赖库
```javascript
// 通过CDN加载的全局库
ECharts 5.x
- 用途: 数据可视化图表
- 加载方式: CDN script标签
- 全局变量: window.echarts
- 版本: 5.4.3+

PapaParse 5.x  
- 用途: CSV文件解析和生成
- 加载方式: CDN script标签
- 全局变量: window.Papa
- 版本: 5.4.1+
```

### API服务
```javascript
// 外部API服务
Kimi API (moonshot.cn)
- 用途: AI对话分析和评估
- 接口: https://api.moonshot.cn/v1/chat/completions
- 认证: Bearer Token
- 模型: moonshot-v1-8k
```

## 开发环境配置 [RS:4]

### 必需工具
```bash
# 代码编辑器
Visual Studio Code 1.80+
- 扩展: ES6 String HTML
- 扩展: Live Server
- 扩展: JavaScript (ES6) code snippets
- 扩展: Bracket Pair Colorizer

# 浏览器开发工具
Google Chrome 120+ (推荐)
- Chrome DevTools
- ES6模块支持
- 现代JavaScript特性支持

# 版本控制
Git 2.30+
- 分支管理
- 提交规范
- 变更跟踪
```

### 可选工具
```bash
# 本地服务器 (可选)
Python 3.x
- python -m http.server 8000

Node.js 18+ (仅用于工具)
- npx http-server
- npx live-server

# 代码质量工具
ESLint (可选)
- 代码规范检查
- 错误检测

Prettier (可选)
- 代码格式化
- 统一代码风格
```

## 项目结构规范 [RS:4]

### 目录结构
```
project-root/
├── index.html / standalone.html    # 主入口文件
├── main.js                        # 应用主模块
├── constants.js                   # 全局常量
├── parser.js                      # 文本解析模块
├── llm.js                        # AI调用模块
├── storage.js                     # 存储管理模块
├── charts.js                     # 图表模块
├── drag-upload.js                 # 文件上传模块
├── config/                        # 配置目录
│   ├── local-config.js           # 本地配置 (gitignore)
│   ├── local-config.example.js   # 配置示例
│   └── api-keys.json             # API密钥 (gitignore)
├── memory-bank/                   # 架构管理目录
│   ├── README.md
│   ├── projectbrief.md
│   └── ...
└── docs/                         # 文档目录
    └── implementation-plans/
```

### 文件命名规范
```javascript
// 模块文件命名
kebab-case.js          // 多词用连字符: drag-upload.js
camelCase.js           // 单词用驼峰: parser.js, storage.js

// 配置文件命名
kebab-case.js          // local-config.js
kebab-case.json        // api-keys.json

// 文档文件命名
kebab-case.md          // implementation-plans.md
camelCase.md           // projectbrief.md (单词)
```

## 编码规范 [RS:4]

### JavaScript规范
```javascript
// 1. 模块导入导出
// 优先使用命名导出
export const functionName = () => {};
export class ClassName {}

// 导入时使用解构
import { functionName, ClassName } from './module.js';

// 2. 函数声明
// 优先使用箭头函数
const processData = (data) => {
  // 函数体
};

// 异步函数使用async/await
const fetchData = async (url) => {
  try {
    const response = await fetch(url);
    return await response.json();
  } catch (error) {
    console.error('获取数据失败:', error);
    throw error;
  }
};

// 3. 变量命名
const MAX_CONCURRENCY = 10;        // 常量: 大写下划线
const apiKey = 'xxx';              // 变量: 驼峰命名
const userDataManager = new Manager(); // 对象: 驼峰命名
```

### 注释规范
```javascript
/**
 * @NAMESPACE QNA_DRIVER_ANALYSIS.DATA.STORAGE
 * @COMPONENT_ID STORAGE_MANAGER_V1
 * @SERVICE 存储管理服务
 * @DESCRIPTION 负责本地存储的读写和数据管理
 */

// @UTIL 工具函数 - 格式化时间戳
const formatTimestamp = (timestamp) => {
  // 实现逻辑...
};

// @EVENT_HANDLER 处理文件上传事件
const handleFileUpload = (event) => {
  // 获取上传的文件列表
  const files = event.target.files;
  // 处理每个文件...
};
```

### CSS规范
```css
/* CSS变量定义 */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
}

/* BEM命名规范 */
.card {}                    /* 块 */
.card__header {}           /* 元素 */
.card--highlighted {}      /* 修饰符 */

/* 响应式设计 */
@media (max-width: 768px) {
  .main-grid {
    grid-template-columns: 1fr;
  }
}
```

## 性能优化策略 [RS:3]

### 加载性能
```javascript
// 1. 模块懒加载
const loadChartModule = async () => {
  const { initializeCharts } = await import('./charts.js');
  return initializeCharts();
};

// 2. CDN资源优化
// 使用defer属性延迟加载非关键资源
<script defer src="https://cdn.jsdelivr.net/npm/echarts@5/dist/echarts.min.js"></script>

// 3. 资源预加载
<link rel="preload" href="./main.js" as="script">
```

### 运行时性能
```javascript
// 1. 防抖和节流
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 2. 内存管理
const cleanup = () => {
  // 清理事件监听器
  element.removeEventListener('click', handler);
  // 清理定时器
  clearInterval(intervalId);
  // 清理大对象引用
  largeDataSet = null;
};

// 3. 批量DOM操作
const fragment = document.createDocumentFragment();
items.forEach(item => {
  const element = createElement(item);
  fragment.appendChild(element);
});
container.appendChild(fragment);
```

## 调试和测试 [RS:3]

### 调试工具
```javascript
// 1. 控制台调试
console.log('调试信息:', data);
console.error('错误信息:', error);
console.time('性能测试');
console.timeEnd('性能测试');

// 2. 断点调试
debugger; // 在关键位置设置断点

// 3. 性能分析
performance.mark('start-processing');
// 处理逻辑...
performance.mark('end-processing');
performance.measure('processing-time', 'start-processing', 'end-processing');
```

### 错误处理
```javascript
// 1. 全局错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error);
  // 错误上报或用户提示
});

// 2. Promise错误处理
window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise拒绝:', event.reason);
  event.preventDefault();
});

// 3. 模块级错误处理
const safeExecute = async (fn, fallback) => {
  try {
    return await fn();
  } catch (error) {
    console.error('执行失败:', error);
    return fallback;
  }
};
```

## 部署和发布 [RS:3]

### 本地开发
```bash
# 1. 直接文件访问
# 在浏览器中打开 file:///path/to/project/index.html

# 2. 本地服务器
cd project-directory
python -m http.server 8000
# 访问 http://localhost:8000

# 3. VS Code Live Server
# 右键HTML文件 -> "Open with Live Server"
```

### 生产部署
```bash
# 1. 静态文件服务器
# 将所有文件上传到Web服务器
# 确保支持HTTPS和现代浏览器

# 2. CDN部署
# 将静态资源部署到CDN
# 配置适当的缓存策略

# 3. 配置检查
# 确保API密钥配置正确
# 验证所有依赖资源可访问
```
