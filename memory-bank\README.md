# Memory-Bank 使用说明

## 概述

Memory-Bank 是 GoMyHire 对话分析平台的统一架构管理和上下文记忆系统。它提供了项目的完整架构视图、组件注册表、依赖关系图和防护机制。

## 目录结构

```
memory-bank/
├── README.md                    # 本文件 - 使用说明
├── projectbrief.md             # 项目核心需求和目标  
├── productContext.md           # 产品背景和解决的问题
├── systemPatterns.md           # 系统架构和技术决策
├── techContext.md              # 技术栈和开发环境
├── activeContext.md            # 当前工作焦点
├── progress.md                 # 进度跟踪
├── personal-memory.md          # 用户偏好
├── architecture/               # 架构管理
│   ├── namespace-registry.md   # 命名空间注册表
│   ├── component-registry.md   # 组件注册表
│   ├── dependency-graph.md     # 依赖关系图
│   ├── interface-definitions.md # 接口定义
│   └── architecture-rules.md   # 架构规则
├── implementation-plans/       # 实施计划
│   └── README.md
└── guards/                     # 防护机制
    ├── conflict-detection.md   # 冲突检测规则
    ├── duplication-prevention.md # 重复开发防护
    └── architecture-validation.md # 架构验证规则
```

## 核心文件说明

### 项目上下文文件
- **projectbrief.md**: 定义项目的核心需求、目标和成功标准
- **productContext.md**: 描述产品存在的原因和解决的业务问题
- **systemPatterns.md**: 记录系统架构模式和关键技术决策
- **techContext.md**: 技术栈选择、开发环境配置和工具链

### 工作状态文件
- **activeContext.md**: 当前正在进行的工作、优先级和下一步计划
- **progress.md**: 已完成的功能、待构建内容和已知问题
- **personal-memory.md**: 用户的个人偏好、编码风格和沟通习惯

### 架构管理文件
- **namespace-registry.md**: 所有命名空间的注册表和分配记录
- **component-registry.md**: 组件清单、状态和元数据
- **dependency-graph.md**: 组件间依赖关系的可视化图表
- **interface-definitions.md**: 标准接口定义和契约规范
- **architecture-rules.md**: 架构约束、设计原则和验证规则

### 防护机制文件
- **conflict-detection.md**: 命名冲突、功能重复的检测规则
- **duplication-prevention.md**: 防止重复开发的检查机制
- **architecture-validation.md**: 架构完整性和一致性验证

## 使用方式

### 开发者工作流
1. 开始新功能开发前，查阅 `activeContext.md` 了解当前工作重点
2. 检查 `component-registry.md` 确认是否已有类似功能
3. 在 `namespace-registry.md` 中注册新组件的命名空间
4. 更新 `dependency-graph.md` 记录新的依赖关系
5. 完成开发后更新 `progress.md` 记录进展

### AI助手工作流
1. 会话开始时读取所有核心文件获取项目上下文
2. 基于 `systemPatterns.md` 和 `architecture-rules.md` 进行架构决策
3. 使用防护机制文件验证变更的合规性
4. 实时更新相关文件反映项目状态变化

## 维护原则

1. **实时更新**: 所有架构变更必须立即反映到相关文档中
2. **版本控制**: 重要变更需要记录变更原因和影响范围
3. **一致性检查**: 定期验证文档间的一致性和完整性
4. **访问控制**: 核心架构文件的修改需要经过审查

## 相关性评分系统

使用 [RS:X] 标记信息的重要性：
- [RS:5]: 关键信息（当前优先级、关键偏好）
- [RS:4]: 高重要性（活跃任务、最近决策）
- [RS:3]: 中等重要性（一般背景、既定模式）
- [RS:2]: 背景信息（历史上下文、过去决策）
- [RS:1]: 外围信息（次要细节、过时信息）
