# 前端模块化方案（纯前端、规避 CORS、扁平结构）

更新时间：2025-08-10

本方案在不引入打包器的前提下，采用原生 ES Modules + 全局 CDN 库（ECharts、PapaParse），保持根目录扁平文件结构；通过 file:// 可直接打开 `index.html`（或现有 `standalone.html`），并提供极简本地服务作为可选项。避免任何跨源请求（CORS）风险：所有资源使用相对路径或 `<script>` 直接内联/同源加载，不做跨域 fetch。

---

## 目标与约束

- 纯前端（No Node/打包器/后端）
- 规避 CORS（不跨源请求第三方 API；若调用 Kimi，使用本地 `config/local-config.js` 注入密钥并从页面端直连官方域名；若浏览器对 file:// ESM 受限，使用同源本地静态服务）
- 扁平文件结构（根目录直放核心模块与入口 HTML），与仓库 `.github/copilot-instructions.md` 指定的模块保持一致

## 拟定的扁平结构（无 src/ 迁移）

- index.html（或复用 standalone.html 并平滑重命名）
- main.js（应用编排，TaskPool、UI 绑定、日志）
- parser.js（文本解析、对话分组、Kimi 评估）
- charts.js（ECharts 初始化/更新）
- storage.js（localStorage 状态与导出 CSV）
- constants.js（常量，包括 MAX_CONCURRENCY）
- drag-upload.js（拖拽/文件选择）
- config/local-config.js（本地密钥，git 忽略；示例在 config/local-config.example.js）

注：ECharts 和 PapaParse 通过 CDN `<script>`（全局 `echarts`、`Papa`），不通过 ESM import 以避免 CORS 与 CSP 兼容性问题。

## 模块边界与接口

- main.js
  - 输入：DOM 事件（file-input、start-btn 等），本地文件 `File`/`Blob`
  - 输出：调用 parser、charts、storage；更新日志区域；任务并发控制
- parser.js
  - `parseTxtContent(content, fileName)`：解析聊天文本，返回标准化消息数组
  - `groupConversationsByQuestion(messages)`：基于结束提示语（如“还有其他问题吗”）分组
  - `evaluateConversationWithKimi(conversation, apiKey)`：直连 <https://api.moonshot.cn/v1/chat/completions>（JSON 首选，正则兜底）
- charts.js
  - `initializeCharts()`：创建 4 个图表实例（chart-questions、chart-effectiveness、chart-satisfaction、chart-knowledge）
  - `updateChartsData(data)`：增量/全量更新图表
- storage.js
  - 使用 localStorage 的 `qna_*` 命名空间；提供合并/导出/清空操作；使用 `Papa.unparse` 导出 CSV（含 UTF-8 BOM）
- constants.js
  - `MAX_CONCURRENCY = 50`；其他常量（选择性）
- drag-upload.js
  - 绑定拖拽/粘贴/选择文件到 `file-input` 与 `#file-list`/`#file-items`

## 规避 CORS 的具体做法

1. 静态资源：
   - 所有应用 JS 使用相对路径 `<script type="module" src="./main.js">` 加载，彼此通过原生 ESM import（同源）。
   - 第三方库（ECharts、PapaParse）使用 `<script src="...cdn..." defer>` 提前注入为全局变量，应用代码以 `window.echarts` 与 `window.Papa` 访问。
2. AI 接口：

- 默认关闭；仅当存在 `window.LOCAL_CONFIG.apiKey` 或 `window.LOCAL_CONFIG.apiKeys.kimi` 时启用。
- 直接向官方域名发起请求：浏览器会执行 CORS 预检；若命中浏览器策略问题，提示用户改为本地极简静态服务（同源页面发起）或使用反向代理服务，但不内置后端。
- 对于 file:// 打开导致 ESM 受限的情况，使用极简静态服务（例如 `python -m http.server 8000` 或 VS Code Live Server）确保同源。

## index.html（或 standalone.html）装载顺序（建议）

1. CDN 库（非模块，defer）：ECharts、PapaParse
2. 本地配置（非模块，defer）：`config/local-config.js`（若存在）
3. 模块入口：`main.js`（`type=module`），内部以 ESM 引入 `parser.js`、`charts.js`、`storage.js`、`constants.js`、`drag-upload.js`

示例（片段）：

```html
<!-- 1) 第三方库：全局变量 -->
<script defer src="https://cdn.jsdelivr.net/npm/echarts@5/dist/echarts.min.js"></script>
<script defer src="https://cdn.jsdelivr.net/npm/papaparse@5/papaparse.min.js"></script>
<!-- 2) 本地配置（可选） -->
<script defer src="./config/local-config.js"></script>
<!-- 3) 应用入口（原生模块） -->
<script type="module" src="./main.js"></script>
```

## DOM 合同（保持不变）

- file-input, start-btn, export-btn, clear-files-btn
- upload-progress, progress-fill, progress-text
- file-list, file-items
- chart-questions, chart-effectiveness, chart-satisfaction, chart-knowledge
- 日志盒：log-box

## 渐进落地步骤（最小改动优先）

- 步骤 A：建立/核对上述 6 个 ESM 模块文件存在与导出接口；如缺失则按约定创建空壳并标注 TODO。
- 步骤 B：检查 `standalone.html` 是否匹配装载顺序；如当前文件为唯一入口，则保留名称并按顺序加载。
- 步骤 C：将第三方库访问统一改用全局 `window.echarts`、`window.Papa`，避免通过 import 导致 CORS。
- 步骤 D：确保 `evaluateConversationWithKimi` 仅在本地配置存在时启用，失败明确日志提示。
- 步骤 E：运行 1–2 个小 `.txt` 文件进行烟测；确认图表渲染、导出 CSV 可下载。

## 回退与兼容

- 若浏览器限制 file:// 执行模块：提供“使用本地静态服务”的引导文案，不强制依赖。
- 保留 `standalone.html` 的同时，可追加 `index.html` 作为别名入口（二选一使用）。

## 验收标准

- 无打包流程，直接打开 HTML 即可运行（或极简本地服务）
- 模块边界清晰，接口稳定；DOM id 与数据契约不变
- 不新增复杂依赖；不产生跨源 fetch 错误
- 通过小样本烟测：解析、图表、导出均工作

## 迁移方式与准则（不增加代码总量）

- 原则：功能零变更、行为零差异；仅做“剪切-粘贴”式代码搬迁；每一步净新增行数≈0（允许少量导入/导出样板行）。
- 批次：单次迁移控制在 200–400 行内，确保可回滚和易审阅；每批迁移后做一次烟测（1–2 个 .txt）。
- 记账：每批记录“从 standalone.html 删除行数 / 新文件增加行数 / 样板行数（import/export/挂载 window）”。
- 命名：保持与本文档约定的文件名与导出名，避免后续重复重命名带来的调整成本。

### 渐进迁移序列（建议）

1. 常量与简单工具

- 从 `standalone.html` 剪切常量（如并发上限、选择器 id、状态枚举）到 `constants.js`；保留 window 桥接（如必要：window.CONSTANTS = { ... }）。
- 风险低，易验证。

2. 存储与导出

- 将 localStorage 读写与 CSV 导出逻辑迁至 `storage.js`，对外保持与 `AppDataManager` 交互接口不变；内部继续调用全局 `Papa`。
- 在页面端仅替换为 `import { ... } from './storage.js'` 的调用点，删除等量内联实现。

3. 图表渲染

- 将 ECharts 初始化/更新封装到 `charts.js`，输入为标准化数据对象；页面保留 DOM id 不变。
- 迁移对应的预览表格填充函数，一并剪切。

4. 解析与分组（含 Kimi 调用）

- 将 `parseTxtContent`、`groupConversationsByQuestion`、`evaluateConversationWithKimi` 迁至 `parser.js`；调用 Kimi 的位置维持不变（仅移动实现）。
- 保持 JSON 优先 + 正则兜底策略。

5. 拖拽/上传与任务编排

- 将拖拽/选择文件、任务队列/并发控制、进度上报剪切到 `drag-upload.js` 与 `main.js`（编排）。
- 继续通过 `TaskPool`（若存在）或等价实现，严格维持行为一致。

6. 报告与问答优化（如需要）

- 将报告生成（进度、导出、历史）与问答标签/去重优化剪切到对应模块（可选新增 `report.js`、`qa-optimize.js`，仍保持扁平结构）。

### 技术策略

- 桥接期 window 兼容：迁移时短期保留 `window.*` 赋值，保证其他未迁移逻辑仍可访问；迁移完成后再统一收敛。
- 模块入口：在 `standalone.html` 按顺序加载 CDN、可选 `config/local-config.js`，最后以 `<script type="module" src="./main.js"></script>` 引入入口；初期 `main.js` 仅做最少量“导线”连接，随着迁移逐步承接逻辑。
- 回滚友好：每步迁移均可独立回滚到上一个已知良好状态。

### 审核与验收（每批次）

- 代码净增≈0 行（记录导入/导出样板行数）。
- DOM 交互、可视化、导出均可用。
- 无新的跨源请求、控制台无新增错误。

## 功能细分模块建议（扁平命名）

在保持“扁平结构 + 纯前端 + 规避 CORS”的前提下，按功能进一步细分模块，文件全部位于仓库根目录：

1. 核心与编排（Core）

- main.js（入口与编排）：应用初始化、实例创建、事件绑定、日志桥接。
- queue.js（ConcurrentQueueManager）：并发队列、失败重试、进度回调。
- file-registry.js（FileRegistry）：文件元数据、状态跟踪、索引与检索。
- progress-manager.js（AnalysisProgressManager）：总体/文件级进度与 UI 同步。

1. 数据与存储（Data）

- constants.js：MAX_CONCURRENCY、枚举（FILE_STATUS、LLM_ENGINES、COMPLEXITY_LEVELS、PRIORITY_LEVELS 等）、DOM id 常量。
- storage.js：STORAGE_KEYS、LEGACY 映射、StorageManager、AppDataManager（CSV 导出仍用 window.Papa）。

1. 解析与 AI（Parsing & AI）

- parser.js：parseTxtContent、parseTimestamp。
- llm.js：evaluateConversationWithKimi、callKimiAPI、parseKimiResponse、repairAndParseJSON、processAnalysisResult。
- api-config.js（APIConfigManager）：密钥读取、启用开关、速率/超时策略（仅数据，不做网络代理）。

1. 可视化与 UI（Charts & UI）

- charts.js：initializeCharts、updateChartsData，严格使用已有容器 id。
- ui-results-table.js：实时结果表格渲染/清空/分页（如内联已实现则剪切）。
- ui-status-panel.js：处理状态面板（进度条、引擎徽章、文件状态列表）。
- ui-tabs.js：顶部标签切换与 active 状态维护。
- drag-upload.js：拖拽/选择/粘贴文件、列表展示、按钮状态。

1. 报告与问答工具（Reports & QA Toolkit）

- report.js（ReportGenerator）：进度、分节生成、历史记录、预览。
- report-exporter.js（ReportExporter）：HTML/PDF/Word 导出（PDF/Word 可占位或按现有实现）。
- qa-tag-analyzer.js（QATagAnalyzer）：标签分析（可调用 llm.js）。
- qa-dedup.js（QADeduplicationManager）：相似度、分组、合并。
- qa-optimize.js（QAOptimizationManager）：批处理编排、历史、统计。

1. 工具与日志（Utils & Logging）

- utils.js：通用函数（格式化、下载、DOM helper、节流/去抖等）。
- logger.js：log(msg) 与 console 映射；必要时挂接到 #log-box。

依赖约束（由低到高）：

- constants.js → utils.js → storage.js → parser.js / llm.js → charts.js / ui-\* → main.js。
- queue.js、file-registry.js、progress-manager.js、api-config.js 为“服务层”，被 main.js/drag-upload.js 调用，但不得反向依赖 UI。
- 禁止环依赖；UI 组件不得调用 LLM，统一通过编排层传参。

接口契约（选摘）：

- queue.js
  - new ConcurrentQueueManager(opts)；onProgress(cb)；addFiles(File[])；start()/pause()/resume()/clear()；getStatus()。
- llm.js
  - evaluateConversationWithKimi(conversationText, apiKey)：Promise&lt;Analysis|Analysis[]&gt;。
- charts.js
  - initializeCharts(): ChartsHandle；updateChartsData(ChartsHandle, data): void。
- storage.js
  - new StorageManager(localStorage)；get/set/merge/clear；exportCsv(data, filename)。
- report.js
  - new ReportGenerator(appDataManager, llm)；generateReport(config)：Promise&lt;Result&gt;；getReportHistory() 等。

迁移命名规范（扁平、短名、领域前缀优先）：

- ui-\*(纯 UI/DOM)
- qa-\*(问答工具)
- report-\*(报告相关)
- \*-manager（状态/服务管理器）
- \*-config / \*-registry / \*-exporter（配置/注册表/导出器）

