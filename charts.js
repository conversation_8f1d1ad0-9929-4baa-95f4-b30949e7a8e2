/**
 * @NAMESPACE QNA_DRIVER_ANALYSIS.UI.CHARTS
 * @COMPONENT_ID CHARTS_MODULE_V1
 * @VERSION 1.0.0
 * @CREATED 2024-06-13
 * @UPDATED 2025-08-10
 * @AUTHOR jc-yap89
 * @DESCRIPTION 图表配置和数据可视化服务，负责ECharts图表的初始化和数据更新
 *
 * @DEPENDENCIES
 * - INPUT: 图表数据对象、DOM容器元素
 * - OUTPUT: createChartConfigs, initializeCharts, updateChartsData
 * - EXTERNAL: window.echarts (CDN加载), DOM API
 *
 * @ARCHITECTURE_LAYER UI
 * @RELATED_MODULES storage.js, parser.js, llm.js
 *
 * @LIFECYCLE_STATUS STABLE
 * @MIGRATION_STATUS COMPLETED
 *
 * @TAGS @SERVICE @COMPONENT @UTIL @UI_COMPONENT
 */

// @UTIL 图表配置生成器
// 创建所有图表的ECharts配置对象，包含样式、数据结构和交互设置
export const createChartConfigs = () => ({
  questions: {
    title: { text: '司机常见问题类别分布', left: 'center' },
    tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
    legend: { orient: 'vertical', left: 'left' },
    series: [{ name: '问题类别', type: 'pie', radius: '50%', data: [], emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0,0,0,0.5)' } } }]
  },
  effectiveness: {
    title: { text: '客服回复有效性分布', left: 'center' },
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    xAxis: { type: 'category', data: ['0-20分','21-40分','41-60分','61-80分','81-100分'] },
    yAxis: { type: 'value', name: '对话数量' },
    series: [{ name: '有效性分布', type: 'bar', data: [], itemStyle: { color: '#5470c6' } }]
  },
  satisfaction: {
    title: { text: '司机满意度趋势', left: 'center' },
    tooltip: { trigger: 'axis' },
    xAxis: { type: 'category', data: [] },
    yAxis: { type: 'value', name: '满意度分数', min: 0, max: 100 },
    series: [{ name: '平均满意度', type: 'line', data: [], smooth: true, itemStyle: { color: '#91cc75' } }]
  },
  knowledge: {
    title: { text: '知识库条目统计', left: 'center' },
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    xAxis: { type: 'category', data: ['总条目数','已分类','待分类','高频问题'] },
    yAxis: { type: 'value', name: '数量' },
    series: [{ name: '知识库统计', type: 'bar', data: [], itemStyle: { color: '#fac858' } }]
  }
});

// @SERVICE 初始化所有图表实例
// 创建并配置所有ECharts图表实例，返回图表对象集合
export const initializeCharts = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const elementIds = ['chart-questions','chart-effectiveness','chart-satisfaction','chart-knowledge'];
      const elements = {};
      const missing = [];
      elementIds.forEach(id => {
        const el = document.getElementById(id);
        if (el) elements[id] = el; else missing.push(id);
      });
      if (missing.length) { console.error('缺少图表容器元素:', missing); resolve(null); return; }
      if (typeof window.echarts === 'undefined') { console.error('ECharts库未加载'); resolve(null); return; }
      try {
        const charts = {
          questions: window.echarts.init(elements['chart-questions']),
          effectiveness: window.echarts.init(elements['chart-effectiveness']),
          satisfaction: window.echarts.init(elements['chart-satisfaction']),
          knowledge: window.echarts.init(elements['chart-knowledge'])
        };
        resolve(charts);
      } catch (e) { console.error('图表初始化失败:', e); resolve(null); }
    }, 200);
  });
};

// @SERVICE 设置图表配置
export const setupCharts = async (charts) => {
  if (!charts) return;
  const configs = createChartConfigs();
  charts.questions.setOption(configs.questions);
  charts.effectiveness.setOption(configs.effectiveness);
  charts.satisfaction.setOption(configs.satisfaction);
  charts.knowledge.setOption(configs.knowledge);
  window.addEventListener('resize', () => { Object.values(charts).forEach(c => c.resize()); });
};

// @SERVICE 更新图表数据显示
// 根据应用数据更新所有图表的显示内容和样式
export const updateChartsData = (charts, appData) => {
  const questionData = [];
  if (appData.questionTags && Object.keys(appData.questionTags).length > 0) {
    Object.entries(appData.questionTags).forEach(([name, value]) => questionData.push({ name, value }));
  } else if (appData.questionCategories) {
    Object.entries(appData.questionCategories).forEach(([name, value]) => questionData.push({ name, value }));
  }
  if (questionData.length) charts.questions.setOption({ series: [{ data: questionData }] });

  if (appData.metrics && appData.metrics.length) {
    const ranges = [0,0,0,0,0];
    appData.metrics.forEach(m => {
      if (m.effectiveness !== undefined) {
        const s = m.effectiveness;
        if (s <= 20) ranges[0]++; else if (s <= 40) ranges[1]++; else if (s <= 60) ranges[2]++; else if (s <= 80) ranges[3]++; else ranges[4]++;
      }
    });
    charts.effectiveness.setOption({ series: [{ data: ranges }] });
  }

  if (appData.drivers) {
    const names = Object.keys(appData.drivers);
    const data = names.map(n => Math.round(appData.drivers[n].avgSatisfaction || 0));
    charts.satisfaction.setOption({ xAxis: { data: names.slice(0,10) }, series: [{ data: data.slice(0,10) }] });
  }

  if (appData.knowledge) {
    const total = appData.knowledge.length;
    const categorized = appData.knowledge.filter(i => i.category !== '待分类').length;
    const uncategorized = total - categorized;
    const highFrequency = appData.knowledge.filter(i => (i.frequency || 0) > 5).length;
    charts.knowledge.setOption({ series: [{ data: [total, categorized, uncategorized, highFrequency] }] });
  }
};

// @REGISTRATION 注册组件到中央注册系统
// 在模块加载时自动注册所有导出的组件
if (typeof window !== 'undefined' && window.centralRegistry) {
  // 注册createChartConfigs工具函数
  window.centralRegistry.registerComponent({
    namespace: 'QNA_DRIVER_ANALYSIS.UI.CHARTS.CREATE_CHART_CONFIGS_V1',
    componentId: 'CREATE_CHART_CONFIGS_V1',
    filePath: 'charts.js',
    type: '@UTIL',
    description: '图表配置生成器',
    dependencies: [],
    status: 'STABLE'
  });

  // 注册initializeCharts服务函数
  window.centralRegistry.registerComponent({
    namespace: 'QNA_DRIVER_ANALYSIS.UI.CHARTS.INITIALIZE_CHARTS_V1',
    componentId: 'INITIALIZE_CHARTS_V1',
    filePath: 'charts.js',
    type: '@SERVICE',
    description: '初始化所有图表实例',
    dependencies: [
      { target: 'window.echarts', type: 'EXTERNAL', description: 'ECharts图表库' },
      { target: 'CREATE_CHART_CONFIGS_V1', type: 'REQUIRED', description: '图表配置生成器' }
    ],
    status: 'STABLE'
  });

  // 注册updateChartsData服务函数
  window.centralRegistry.registerComponent({
    namespace: 'QNA_DRIVER_ANALYSIS.UI.CHARTS.UPDATE_CHARTS_DATA_V1',
    componentId: 'UPDATE_CHARTS_DATA_V1',
    filePath: 'charts.js',
    type: '@SERVICE',
    description: '更新图表数据显示',
    dependencies: [
      { target: 'INITIALIZE_CHARTS_V1', type: 'REQUIRED', description: '图表实例' }
    ],
    status: 'STABLE'
  });

  console.log('✅ charts.js 组件已注册到中央注册系统');
}
