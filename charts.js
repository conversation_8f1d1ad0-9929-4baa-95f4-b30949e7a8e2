/**
 * charts.js - 图表配置和数据可视化服务
 * 依赖：全局 window.echarts（通过 CDN 注入）
 */

// @UTIL 图表配置生成器
export const createChartConfigs = () => ({
  questions: {
    title: { text: '司机常见问题类别分布', left: 'center' },
    tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
    legend: { orient: 'vertical', left: 'left' },
    series: [{ name: '问题类别', type: 'pie', radius: '50%', data: [], emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0,0,0,0.5)' } } }]
  },
  effectiveness: {
    title: { text: '客服回复有效性分布', left: 'center' },
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    xAxis: { type: 'category', data: ['0-20分','21-40分','41-60分','61-80分','81-100分'] },
    yAxis: { type: 'value', name: '对话数量' },
    series: [{ name: '有效性分布', type: 'bar', data: [], itemStyle: { color: '#5470c6' } }]
  },
  satisfaction: {
    title: { text: '司机满意度趋势', left: 'center' },
    tooltip: { trigger: 'axis' },
    xAxis: { type: 'category', data: [] },
    yAxis: { type: 'value', name: '满意度分数', min: 0, max: 100 },
    series: [{ name: '平均满意度', type: 'line', data: [], smooth: true, itemStyle: { color: '#91cc75' } }]
  },
  knowledge: {
    title: { text: '知识库条目统计', left: 'center' },
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    xAxis: { type: 'category', data: ['总条目数','已分类','待分类','高频问题'] },
    yAxis: { type: 'value', name: '数量' },
    series: [{ name: '知识库统计', type: 'bar', data: [], itemStyle: { color: '#fac858' } }]
  }
});

// @UTIL 初始化所有图表
export const initializeCharts = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const elementIds = ['chart-questions','chart-effectiveness','chart-satisfaction','chart-knowledge'];
      const elements = {};
      const missing = [];
      elementIds.forEach(id => {
        const el = document.getElementById(id);
        if (el) elements[id] = el; else missing.push(id);
      });
      if (missing.length) { console.error('缺少图表容器元素:', missing); resolve(null); return; }
      if (typeof window.echarts === 'undefined') { console.error('ECharts库未加载'); resolve(null); return; }
      try {
        const charts = {
          questions: window.echarts.init(elements['chart-questions']),
          effectiveness: window.echarts.init(elements['chart-effectiveness']),
          satisfaction: window.echarts.init(elements['chart-satisfaction']),
          knowledge: window.echarts.init(elements['chart-knowledge'])
        };
        resolve(charts);
      } catch (e) { console.error('图表初始化失败:', e); resolve(null); }
    }, 200);
  });
};

// @SERVICE 设置图表配置
export const setupCharts = async (charts) => {
  if (!charts) return;
  const configs = createChartConfigs();
  charts.questions.setOption(configs.questions);
  charts.effectiveness.setOption(configs.effectiveness);
  charts.satisfaction.setOption(configs.satisfaction);
  charts.knowledge.setOption(configs.knowledge);
  window.addEventListener('resize', () => { Object.values(charts).forEach(c => c.resize()); });
};

// @SERVICE 更新图表数据
export const updateChartsData = (charts, appData) => {
  const questionData = [];
  if (appData.questionTags && Object.keys(appData.questionTags).length > 0) {
    Object.entries(appData.questionTags).forEach(([name, value]) => questionData.push({ name, value }));
  } else if (appData.questionCategories) {
    Object.entries(appData.questionCategories).forEach(([name, value]) => questionData.push({ name, value }));
  }
  if (questionData.length) charts.questions.setOption({ series: [{ data: questionData }] });

  if (appData.metrics && appData.metrics.length) {
    const ranges = [0,0,0,0,0];
    appData.metrics.forEach(m => {
      if (m.effectiveness !== undefined) {
        const s = m.effectiveness;
        if (s <= 20) ranges[0]++; else if (s <= 40) ranges[1]++; else if (s <= 60) ranges[2]++; else if (s <= 80) ranges[3]++; else ranges[4]++;
      }
    });
    charts.effectiveness.setOption({ series: [{ data: ranges }] });
  }

  if (appData.drivers) {
    const names = Object.keys(appData.drivers);
    const data = names.map(n => Math.round(appData.drivers[n].avgSatisfaction || 0));
    charts.satisfaction.setOption({ xAxis: { data: names.slice(0,10) }, series: [{ data: data.slice(0,10) }] });
  }

  if (appData.knowledge) {
    const total = appData.knowledge.length;
    const categorized = appData.knowledge.filter(i => i.category !== '待分类').length;
    const uncategorized = total - categorized;
    const highFrequency = appData.knowledge.filter(i => (i.frequency || 0) > 5).length;
    charts.knowledge.setOption({ series: [{ data: [total, categorized, uncategorized, highFrequency] }] });
  }
};
