# Copilot instructions for this repository (driver QnA app)

- Context first: read `CLAUDE.md`, `architecture-decision.md`, `docs/refactor-notes.md`, and memory-bank core docs before edits.
- Align with `.github/instructions/ini.instructions.md`: plan-first, doc-first, avoid circular deps, small focused diffs, no secrets.
- Source of truth: use root ESM modules (`main.js`, `parser.js`, `charts.js`, `storage.js`, `constants.js`, `drag-upload.js`) until a `src/` migration lands.
- Runtime entry: root `index.html` (single page) loads ECharts and PapaParse via CDN; avoid bundler-only APIs.
- UI contract (DOM ids): `file-input`, `start-btn`, `export-btn`, `clear-files-btn`, `upload-progress`, `progress-fill`, `progress-text`, `file-list`, `file-items`.
- Chart containers: `chart-questions`, `chart-effectiveness`, `chart-satisfaction`, `chart-knowledge` (required by `charts.js`). Don’t rename without updating both HTML and JS.
- Libraries: ECharts 5.x (global `echarts`), PapaParse 5.x (global `Papa`).
- Concurrency: respect `MAX_CONCURRENCY = 50` in `constants.js`; keep API work inside `TaskPool` in `main.js`.
- Parsing: `parser.js::parseTxtContent(content, fileName)` expects `HH:MM Driver|Support Name: Message`; groups via `groupConversationsByQuestion` (e.g., end cues “还有其他问题吗”). Keep regex additions conservative and tested.
- AI calls: `evaluateConversationWithKimi(conversation, apiKey)` → `https://api.moonshot.cn/v1/chat/completions`, model `kimi-k2-turbo-preview`; parse JSON then regex-fallback. API Key 通过 `window.LOCAL_CONFIG.apiKey` 或 `window.LOCAL_CONFIG.apiKeys.kimi` 自动加载；无需 UI 输入。
- Storage schema: `storage.js` uses localStorage `qna_*` keys (drivers, customer_service, knowledge, metrics, question_categories, settings). If you find `enhanced_qna_*`, migrate to `qna_*`.
- Data contract: preserve shapes used by `AppDataManager` (merge/update methods, CSV exports) when extending fields.
- Charts: initialize via `initializeCharts()` then update with `updateChartsData(...)`. Keep option/update paths stable.
- Exports: use Papa.unparse in `storage.js`; for new exporters ensure UTF-8 BOM for Excel compatibility.
- Logging/UI: append to `#log-box` in `main.js`. Keep non-blocking; surface failures clearly.
- Error handling: don’t swallow exceptions; log user-facing errors and propagate where appropriate.
- Dependencies: prefer existing utilities; don’t add new libraries without a brief justification in `docs/refactor-notes.md`.
- Serving: supports direct file open via `index.html` (file://). 若需本地密钥，创建 `config/local-config.js` 并定义 `window.LOCAL_CONFIG = { apiKey: 'sk-***' }`（文件不入库）。如浏览器阻止 file:// ESM，引导使用极简静态服务。
- Touch points for features: typically `main.js` (orchestration), `parser.js` (parsing/AI), `charts.js` (visuals), `storage.js` (state/exports). Update docs atomically.
- Renames: if changing DOM ids or storage keys, update all references and provide a tiny migration in `storage.js`.
- Tests/verification: run a smoke test with 1–2 small `.txt` files; confirm charts render and CSV exports download.
- Commit hygiene: small diffs, clear messages referencing concrete modules and reasons; avoid mixing refactors with features.
- Large refactors: draft a short plan in `docs/implementation-plans/` and get approval before executing.
- Regex fallback: keep JSON-first parsing from Kimi; don’t loosen regex without adding a sample to `tests/` (if present).
- Style: follow existing naming/import patterns; ES modules and top-level exports only; concise comments over boilerplate.
